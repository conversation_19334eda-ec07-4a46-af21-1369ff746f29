/**
 * POS Feature Settings Component
 * Allows users to enable/disable POS features like voucher, service charges, etc.
 */

import React, { useState, useEffect } from 'react';
import { 
  getPOSFeatureSettings, 
  savePOSFeatureSettings,
  setVoucherEnabled,
  isVoucherEnabled 
} from '../../../core/utils/malaysianTax';

const POSFeatureSettings = () => {
  const [settings, setSettings] = useState({
    enableVoucher: false,
    enableServiceCharge: true,
    enableRounding: true,
    enableDiscount: true,
    enableTax: true
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  // Load settings on component mount
  useEffect(() => {
    const loadSettings = () => {
      try {
        const currentSettings = getPOSFeatureSettings();
        setSettings(currentSettings);
      } catch (error) {
        console.error('Error loading POS feature settings:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadSettings();
  }, []);

  // Handle setting toggle
  const handleToggleSetting = async (settingKey, enabled) => {
    try {
      setIsSaving(true);
      
      const newSettings = { ...settings, [settingKey]: enabled };
      setSettings(newSettings);
      
      // Save to localStorage
      savePOSFeatureSettings(newSettings);
      
      // Special handling for voucher setting
      if (settingKey === 'enableVoucher') {
        setVoucherEnabled(enabled);
      }
      
      // Show success message
      console.log(`${settingKey} ${enabled ? 'enabled' : 'disabled'} successfully`);
      
      // Trigger page refresh to update UI components
      setTimeout(() => {
        window.location.reload();
      }, 500);
      
    } catch (error) {
      console.error('Error updating POS feature setting:', error);
      // Revert the change on error
      setSettings(settings);
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="d-flex justify-content-center align-items-center p-4">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="card-header">
        <h5 className="card-title mb-0">
          <i className="ti ti-settings me-2"></i>
          POS Feature Settings
        </h5>
      </div>
      <div className="card-body">
        <div className="row g-3">
          
          {/* Voucher Setting */}
          <div className="col-12">
            <div className="d-flex justify-content-between align-items-center p-3 border rounded">
              <div>
                <h6 className="mb-1">Voucher Feature</h6>
                <small className="text-muted">
                  Enable or disable voucher functionality in POS
                </small>
              </div>
              <div className="form-check form-switch">
                <input
                  className="form-check-input"
                  type="checkbox"
                  id="enableVoucher"
                  checked={settings.enableVoucher}
                  onChange={(e) => handleToggleSetting('enableVoucher', e.target.checked)}
                  disabled={isSaving}
                />
                <label className="form-check-label" htmlFor="enableVoucher">
                  {settings.enableVoucher ? 'Enabled' : 'Disabled'}
                </label>
              </div>
            </div>
          </div>

          {/* Service Charge Setting */}
          <div className="col-12">
            <div className="d-flex justify-content-between align-items-center p-3 border rounded">
              <div>
                <h6 className="mb-1">Service Charge</h6>
                <small className="text-muted">
                  Enable or disable service charge calculations
                </small>
              </div>
              <div className="form-check form-switch">
                <input
                  className="form-check-input"
                  type="checkbox"
                  id="enableServiceCharge"
                  checked={settings.enableServiceCharge}
                  onChange={(e) => handleToggleSetting('enableServiceCharge', e.target.checked)}
                  disabled={isSaving}
                />
                <label className="form-check-label" htmlFor="enableServiceCharge">
                  {settings.enableServiceCharge ? 'Enabled' : 'Disabled'}
                </label>
              </div>
            </div>
          </div>

          {/* Rounding Setting - Now follows Invoice Settings */}
          <div className="col-12">
            <div className="d-flex justify-content-between align-items-center p-3 border rounded bg-light">
              <div>
                <h6 className="mb-1">Malaysian Rounding</h6>
                <small className="text-muted">
                  Rounding setting is controlled from Invoice Settings
                </small>
              </div>
              <div className="text-muted">
                <small>
                  <i className="ti ti-settings me-1"></i>
                  Managed in Invoice Settings
                </small>
              </div>
            </div>
          </div>

          {/* Discount Setting */}
          <div className="col-12">
            <div className="d-flex justify-content-between align-items-center p-3 border rounded">
              <div>
                <h6 className="mb-1">Discount Feature</h6>
                <small className="text-muted">
                  Enable or disable discount functionality
                </small>
              </div>
              <div className="form-check form-switch">
                <input
                  className="form-check-input"
                  type="checkbox"
                  id="enableDiscount"
                  checked={settings.enableDiscount}
                  onChange={(e) => handleToggleSetting('enableDiscount', e.target.checked)}
                  disabled={isSaving}
                />
                <label className="form-check-label" htmlFor="enableDiscount">
                  {settings.enableDiscount ? 'Enabled' : 'Disabled'}
                </label>
              </div>
            </div>
          </div>

          {/* Tax Setting */}
          <div className="col-12">
            <div className="d-flex justify-content-between align-items-center p-3 border rounded">
              <div>
                <h6 className="mb-1">Tax Calculation</h6>
                <small className="text-muted">
                  Enable or disable tax calculations (Malaysian tax logic)
                </small>
              </div>
              <div className="form-check form-switch">
                <input
                  className="form-check-input"
                  type="checkbox"
                  id="enableTax"
                  checked={settings.enableTax}
                  onChange={(e) => handleToggleSetting('enableTax', e.target.checked)}
                  disabled={isSaving}
                />
                <label className="form-check-label" htmlFor="enableTax">
                  {settings.enableTax ? 'Enabled' : 'Disabled'}
                </label>
              </div>
            </div>
          </div>

        </div>

        {/* Current Status */}
        <div className="mt-4 p-3 bg-light rounded">
          <h6 className="mb-2">Current Status:</h6>
          <div className="row g-2">
            <div className="col-6">
              <small className="text-muted">Voucher:</small>
              <span className={`ms-2 badge ${settings.enableVoucher ? 'bg-success' : 'bg-secondary'}`}>
                {settings.enableVoucher ? 'ON' : 'OFF'}
              </span>
            </div>
            <div className="col-6">
              <small className="text-muted">Service Charge:</small>
              <span className={`ms-2 badge ${settings.enableServiceCharge ? 'bg-success' : 'bg-secondary'}`}>
                {settings.enableServiceCharge ? 'ON' : 'OFF'}
              </span>
            </div>
            <div className="col-6">
              <small className="text-muted">Rounding:</small>
              <span className="ms-2 badge bg-info">
                Invoice Settings
              </span>
            </div>
            <div className="col-6">
              <small className="text-muted">Discount:</small>
              <span className={`ms-2 badge ${settings.enableDiscount ? 'bg-success' : 'bg-secondary'}`}>
                {settings.enableDiscount ? 'ON' : 'OFF'}
              </span>
            </div>
          </div>
        </div>

        {/* Note */}
        <div className="alert alert-info mt-3 mb-0">
          <i className="ti ti-info-circle me-2"></i>
          <strong>Note:</strong> Changes will take effect immediately. The page will refresh automatically to update all UI components.
        </div>
      </div>
    </div>
  );
};

export default POSFeatureSettings;
