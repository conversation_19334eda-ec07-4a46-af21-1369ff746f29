# Voucher Settings Guide

## **Overview**
The POS system now includes a setting to enable or disable the voucher feature. The voucher setting is integrated into the **Invoice Settings** under **App Settings** and is **disabled by default** as a disabled feature. When disabled, vouchers will not be applied to calculations even if voucher amounts are present.

## **New Functions Added:**

### **1. Check Voucher Status**
```javascript
import { isVoucherEnabled } from './core/utils/malaysianTax';

// Check if voucher is currently enabled
const voucherEnabled = isVoucherEnabled();
console.log('Voucher enabled:', voucherEnabled); // false by default
```

### **2. Enable/Disable Voucher**
```javascript
import { setVoucherEnabled } from './core/utils/malaysianTax';

// Disable voucher feature
setVoucherEnabled(false);

// Enable voucher feature
setVoucherEnabled(true);
```

### **3. Get All POS Feature Settings**
```javascript
import { getPOSFeatureSettings } from './core/utils/malaysianTax';

const settings = getPOSFeatureSettings();
console.log(settings);
// Output:
// {
//   enableVoucher: false,        // Voucher feature disabled by default
//   enableServiceCharge: true,   // Service charge enabled
//   enableRounding: true,        // Rounding enabled
//   enableDiscount: true,        // Discount enabled
//   enableTax: true             // Tax enabled
// }
```

### **4. Save Custom Settings**
```javascript
import { savePOSFeatureSettings } from './core/utils/malaysianTax';

const newSettings = {
  enableVoucher: true,         // Enable voucher
  enableServiceCharge: true,
  enableRounding: true,
  enableDiscount: true,
  enableTax: true
};

savePOSFeatureSettings(newSettings);
```

## **How It Works:**

### **Default Behavior:**
- **Voucher is DISABLED by default** (disabled feature)
- Setting is located in **App Settings > Invoice Settings**
- When disabled, voucher amounts are ignored in calculations
- Other features (tax, service charge, discount, rounding) remain enabled

### **Calculation Logic:**
```javascript
// In calculateOrderTotal():
let voucherAmount = 0;
if (isVoucherEnabled()) {
  voucherAmount = parseFloat(options.voucher || savedPaymentSummary.voucher || 0);
}

// Final total calculation:
let total = orderSubtotal - discountAmount + serviceCharges - voucherAmount;
```

## **How to Enable/Disable Voucher:**

### **Method 1: Through Invoice Settings (Recommended)**
1. Go to **Settings** in the main menu
2. Navigate to **App Settings > Invoice**
3. Find **"Enable Voucher Feature"** setting
4. Toggle the switch to enable/disable
5. The change takes effect immediately

### **Method 2: Programmatically**
```javascript
import { setVoucherEnabled } from './core/utils/malaysianTax';

// Disable voucher system-wide
setVoucherEnabled(false);

// Enable voucher system-wide
setVoucherEnabled(true);
```

## **Usage Examples:**

### **Example 1: Check Current Status**
```javascript
import { isVoucherEnabled } from './core/utils/malaysianTax';

const enabled = isVoucherEnabled();
console.log('Voucher enabled:', enabled); // false by default
```

### **Example 2: Enable Voucher for Transactions**
```javascript
// Enable through Invoice Settings UI, then vouchers work normally
const total = calculateOrderTotal({
  voucher: 10.00  // This will be applied if voucher is enabled
});
```

### **Example 3: Check Status Before Showing Voucher UI**
```javascript
import { isVoucherEnabled } from './core/utils/malaysianTax';

// In your React component
const VoucherInput = () => {
  const voucherEnabled = isVoucherEnabled();
  
  if (!voucherEnabled) {
    return null; // Don't show voucher input if disabled
  }
  
  return (
    <div>
      <label>Voucher Amount:</label>
      <input type="number" placeholder="Enter voucher amount" />
    </div>
  );
};
```

## **Settings Storage:**
- Settings are stored in localStorage under key: `'pos_feature_settings'`
- Settings persist across browser sessions
- Settings are applied system-wide

## **Default Settings:**
```javascript
{
  enableVoucher: false,        // ❌ DISABLED by default
  enableServiceCharge: true,   // ✅ Enabled
  enableRounding: true,        // ✅ Enabled
  enableDiscount: true,        // ✅ Enabled
  enableTax: true             // ✅ Enabled
}
```

## **Integration with POS Components:**

### **In POS Modal Components:**
```javascript
import { isVoucherEnabled } from '../../core/utils/malaysianTax';

// Check if voucher should be shown in payment modal
const showVoucherInput = isVoucherEnabled();

// Conditionally render voucher input
{showVoucherInput && (
  <div className="voucher-section">
    <label>Voucher Amount</label>
    <input type="number" name="voucher" />
  </div>
)}
```

### **In Settings Component:**
```javascript
import { getPOSFeatureSettings, savePOSFeatureSettings } from '../../core/utils/malaysianTax';

const POSSettings = () => {
  const [settings, setSettings] = useState(getPOSFeatureSettings());
  
  const handleToggleVoucher = (enabled) => {
    const newSettings = { ...settings, enableVoucher: enabled };
    setSettings(newSettings);
    savePOSFeatureSettings(newSettings);
  };
  
  return (
    <div>
      <label>
        <input 
          type="checkbox" 
          checked={settings.enableVoucher}
          onChange={(e) => handleToggleVoucher(e.target.checked)}
        />
        Enable Voucher Feature
      </label>
    </div>
  );
};
```

## **Benefits:**
1. **Flexibility**: Can enable/disable voucher feature as needed
2. **Clean UI**: Hide voucher inputs when feature is disabled
3. **Accurate Calculations**: Vouchers only applied when enabled
4. **Easy Management**: Simple functions to control the feature
5. **Persistent Settings**: Settings saved across sessions

## **Quick Commands:**

```javascript
// Disable voucher (default)
setVoucherEnabled(false);

// Enable voucher
setVoucherEnabled(true);

// Check status
const enabled = isVoucherEnabled();
```

The voucher feature is now fully controllable and disabled by default, giving you complete control over when and how vouchers are used in your POS system.
