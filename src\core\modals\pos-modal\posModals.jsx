import React, { useState, useRef, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import Image<PERSON>ithBasePath from '../../img/imagewithbasebath'
import { Edit, Eye, Trash2 } from 'feather-icons-react/build/IconComponents'
import TooltipIcons from '../../common/tooltip-content/tooltipIcons'
import { Tooltip } from 'antd'
import Select from "react-select"
import {
  printReceipt,
  extractReceiptDataFromDOM,
  testPrinterConnection,
  getSavedPrinterInfo,
  clearSavedPrinterInfo,
  isBluetoothSupported,
  discoverBluetoothPrinter
} from '../../utils/thermalPrinter';
import {
  saveDiscountSettings,
  getDiscountSettings,
  savePaymentSummary,
  getPaymentSummary,
  calculateOrderTotal
} from '../../utils/orderStorage';
import {
  addCustomer,
  setSelectedCustomer,
  saveOrderForCustomer,
  getOrdersForCustomer,
  loadSavedOrder,
  deleteSavedOrder
} from '../../utils/customerStorage';
import {
  addPaymentToCurrentOrder,
  getCurrentOrderPayments,
  calculateRemainingBalance,
  formatPaymentCurrency,
  calculateTotalPaid
} from '../../utils/paymentStorage';
import { getTaxCategoriesForSelect, initializeTaxCategories } from '../../utils/taxCategories';
import TaxRatesService from '../../services/taxRates.service';
import CompanyService from '../../services/company.service';
import TaxCategoriesModal from './taxCategoriesModal';
import { formatCurrency } from '../../utils/currency';
import {
  getOrders,
  clearOrders,
  getDiscountSettings as getOrderDiscountSettings,
  getPaymentSummary as getOrderPaymentSummary
} from '../../utils/orderStorage';
import { isVoucherEnabled } from '../../utils/malaysianTax';

const PosModals = ({ onReset }) => {
    const [input, setInput] = useState("");
    const [isPrinting, setIsPrinting] = useState(false);
    const [printerInfo, setPrinterInfo] = useState(null);
    const [bluetoothSupported, setBluetoothSupported] = useState(false);
    const [discountSettings, setDiscountSettings] = useState({
      discountType: 'percentage',
      discountValue: 0,
      customPercentage: 0
    });
    const [paymentSummary, setPaymentSummary] = useState({
      serviceCharges: 0,
      tax: 0,
      taxRate: 0,
      voucher: 0,
      roundOffAmount: 0
      // roundOff removed - now follows Invoice Settings automatically
    });
    const [taxRatesFromAPI, setTaxRatesFromAPI] = useState([]);
    const [loadingTaxRates, setLoadingTaxRates] = useState(false);
    const [companyTaxSettings, setCompanyTaxSettings] = useState(null);

    // New state for tax category selection
    const [taxCategories, setTaxCategories] = useState([]);
    const [selectedTaxCategory, setSelectedTaxCategory] = useState(null);
    const [availableTaxRates, setAvailableTaxRates] = useState([]);
    const [selectedTaxRate, setSelectedTaxRate] = useState(null);

    // Company profile state for service charges
    const [companyProfile, setCompanyProfile] = useState(null);
    const [cashPaymentForm, setCashPaymentForm] = useState({
      receivedAmount: '',
      payingAmount: '',
      change: 0,
      paymentType: 'cash',
      paymentReceiver: '',
      paymentNote: '',
      saleNote: '',
      staffNote: ''
    });
    const [orderTotal, setOrderTotal] = useState(0);
    const receiptRef = useRef(null);

    // Customer form state
    const [customerForm, setCustomerForm] = useState({
      code: '',
      name: '',
      description: '',
      isTaxExempt: false,
      isPICEditable: true,
      defaultPIC: '',
      accountCode: '',
      remark: '',
      identityTypeId: null,
      identityNo: '',
      fullName: '',
      email: '',
      tinNo: '',
      tinVerifyStatus: 0,
      debtorTypeId: null,
      referrerId: null,
      currencyId: null,
      accountGroupId: null,
      companyId: null,
      // Address fields
      firstName: '',
      lastName: '',
      address1: '',
      address2: '',
      address3: '',
      postalCode: '',
      phone: '',
      faxNo: '',
      coordinate: '',
      countryId: null,
      stateProvinceId: null,
      regionId: null
    });
    const [customerFormErrors, setCustomerFormErrors] = useState({});
    const [isCreatingCustomer, setIsCreatingCustomer] = useState(false);

    // Order saving state
    const [isSavingOrder, setIsSavingOrder] = useState(false);
    const [customerOrders, setCustomerOrders] = useState([]);
    const [selectedCustomerForOrders, setSelectedCustomerForOrders] = useState(null);
    const [orderNotes, setOrderNotes] = useState('');

    // Function to load tax categories and company settings from API with enhanced error handling
    const loadTaxRatesFromAPI = async (retryCount = 0) => {
      const maxRetries = 2;

      try {
        setLoadingTaxRates(true);

        // Load company profile, company tax settings and tax categories in parallel
        const [companyProfileData, companySettings, taxCategoriesForPOS] = await Promise.all([
          CompanyService.refreshCompanyProfile().catch(err => {
            console.warn('Failed to load fresh company profile, trying cached:', err);
            // Fallback to cached data if refresh fails
            return CompanyService.getCachedCompanyInfo();
          }),
          CompanyService.getCompanyTaxSettings().catch(err => {
            console.warn('Failed to load company tax settings:', err);
            return null;
          }),
          TaxRatesService.getTaxCategoriesForPOS()
        ]);

        // Update company profile and tax settings
        if (companyProfileData) {
          setCompanyProfile(companyProfileData);
        }
        if (companySettings) {
          setCompanyTaxSettings(companySettings);
        }

        if (taxCategoriesForPOS && taxCategoriesForPOS.length > 0) {
          setTaxCategories(taxCategoriesForPOS);
          console.log(`Successfully loaded ${taxCategoriesForPOS.length} tax categories from API`);

          // Set default tax category and rate from company profile
          if (companyProfileData && companyProfileData.result) {
            await setDefaultTaxFromCompanyProfile(companyProfileData.result, taxCategoriesForPOS);
          }
        } else {
          throw new Error('No tax categories received from API');
        }
      } catch (error) {
        console.warn(`Failed to load tax categories from API (attempt ${retryCount + 1}):`, error);

        // Retry logic
        if (retryCount < maxRetries) {
          setTimeout(() => loadTaxRatesFromAPI(retryCount + 1), 1000);
          return;
        }

        // Final fallback to local tax categories
        console.log('Using local tax categories as fallback');
        const localTaxRates = getTaxCategoriesForSelect();
        setTaxRatesFromAPI(localTaxRates);
      } finally {
        setLoadingTaxRates(false);
      }
    };

    // Function to set default tax category and rate from company profile
    const setDefaultTaxFromCompanyProfile = async (companyProfile, availableCategories) => {
      try {
        // Get the company's tax category from the profile
        const companyTaxCategory = companyProfile.taxCategory;

        if (!companyTaxCategory) {
          console.log('No tax category found in company profile');
          return;
        }

        console.log('Company tax category from profile:', companyTaxCategory);

        // Find matching tax category in available categories
        const matchingCategory = availableCategories.find(cat =>
          cat.id === companyTaxCategory.id || cat.code === companyTaxCategory.code
        );

        if (matchingCategory) {
          console.log(`Setting default tax category: ${matchingCategory.displayName}`);
          console.log('Matching category details:', matchingCategory);

          // Step 1: Set the selected tax category (ensure hasRates is set correctly)
          const categoryWithRates = {
            ...matchingCategory,
            hasRates: matchingCategory.hasRates !== false && matchingCategory.code !== 'NOTAX'
          };
          setSelectedTaxCategory(categoryWithRates);

          // Step 2: Load ALL tax rates using ServiceTax method and set default selection
          console.log('Loading all tax rates for dropdown...');
          const allTaxRates = await TaxRatesService.getAllTaxRatesForDropdown();
          console.log('Raw tax rates from API:', allTaxRates);
          setAvailableTaxRates(allTaxRates);
          console.log(`Loaded ${allTaxRates.length} total tax rates from API for dropdown`);

          // Determine which default tax rate to use based on company profile
          let defaultTaxRate = null;

          // Check for defaultSalesTaxNo (for SALESTAX companies)
          if (companyProfile.defaultSalesTaxNo && companyTaxCategory.code === 'SALESTAX') {
            defaultTaxRate = allTaxRates.find(rate =>
              rate.id === companyProfile.defaultSalesTaxNo.id ||
              rate.code === companyProfile.defaultSalesTaxNo.code
            );
            console.log('Using defaultSalesTaxNo for SALESTAX company:', defaultTaxRate);
          }
          // Check for defaultServiceTaxNo (for SERVICETAX companies)
          else if (companyProfile.defaultServiceTaxNo && companyTaxCategory.code === 'SERVICETAX') {
            defaultTaxRate = allTaxRates.find(rate =>
              rate.id === companyProfile.defaultServiceTaxNo.id ||
              rate.code === companyProfile.defaultServiceTaxNo.code
            );
            console.log('Using defaultServiceTaxNo for SERVICETAX company:', defaultTaxRate);
          }
          // Fallback: use the first tax rate from company's taxCategory.taxRate array
          else if (companyTaxCategory.taxRate && companyTaxCategory.taxRate.length > 0) {
            const firstCompanyTaxRate = companyTaxCategory.taxRate[0];
            defaultTaxRate = allTaxRates.find(rate =>
              rate.id === firstCompanyTaxRate.id ||
              rate.code === firstCompanyTaxRate.code
            );
            console.log('Using first tax rate from company taxCategory.taxRate array:', defaultTaxRate);
          }

          // Set the default tax rate (this will pre-select it in the dropdown)
          if (defaultTaxRate) {
            console.log(`Setting default tax rate: ${defaultTaxRate.name} (${defaultTaxRate.chargePercentage}%)`);
            setSelectedTaxRate(defaultTaxRate);

            // Update payment summary with the default tax rate
            const newPaymentSummary = { ...paymentSummary, taxRate: defaultTaxRate.chargePercentage };
            setPaymentSummary(newPaymentSummary);
            savePaymentSummary(newPaymentSummary);
            recalculateOrderTotal(discountSettings, newPaymentSummary);
          } else {
            console.log('No matching default tax rate found in available rates');
            // Clear any previous selection but keep the dropdown available
            setSelectedTaxRate(null);
          }

          // Handle NOTAX category special case
          if (companyTaxCategory.code === 'NOTAX') {
            console.log('Setting tax rate to 0 for NOTAX category');
            setSelectedTaxRate(null);
            const newPaymentSummary = { ...paymentSummary, taxRate: 0 };
            setPaymentSummary(newPaymentSummary);
            savePaymentSummary(newPaymentSummary);
            recalculateOrderTotal(discountSettings, newPaymentSummary);
          }
        } else {
          console.log(`No matching tax category found for company tax category: ${companyTaxCategory.code}`);
        }
      } catch (error) {
        console.error('Error setting default tax from company profile:', error);
      }
    };

    // Function to handle tax category selection
    const handleTaxCategorySelect = async (category) => {
      try {
        setSelectedTaxCategory(category);

        // Always clear previous tax rate selection when changing category
        setSelectedTaxRate(null);

        // Use ServiceTax approach - get ALL tax rates from API
        console.log('handleTaxCategorySelect: Loading all tax rates...');
        const allTaxRates = await TaxRatesService.getAllTaxRatesForDropdown();
        console.log('handleTaxCategorySelect: Raw tax rates from API:', allTaxRates);
        setAvailableTaxRates(allTaxRates);

        if (process.env.NODE_ENV === 'development') {
          console.log(`Selected tax category: ${category.displayName}`);
          console.log(`Available tax rates from API:`, allTaxRates);
        }

        // Handle NOTAX category special case
        if (category.code === 'NOTAX') {
          // Set tax rate to 0 for NOTAX
          const newPaymentSummary = { ...paymentSummary, taxRate: 0 };
          setPaymentSummary(newPaymentSummary);
          savePaymentSummary(newPaymentSummary);
          recalculateOrderTotal(discountSettings, newPaymentSummary);
        }
      } catch (error) {
        console.error('Error loading tax rates for category:', error);
        setAvailableTaxRates([]);
        setSelectedTaxRate(null);
      }
    };

    // Function to handle tax rate selection
    const handleTaxRateSelect = (taxRate) => {
      setSelectedTaxRate(taxRate);

      // Update payment summary with selected tax rate
      const newPaymentSummary = { ...paymentSummary, taxRate: taxRate.chargePercentage };
      setPaymentSummary(newPaymentSummary);
      savePaymentSummary(newPaymentSummary);
      recalculateOrderTotal(discountSettings, newPaymentSummary);
    };

    // Load saved printer info on component mount
    useEffect(() => {
      // Check if Bluetooth is supported
      setBluetoothSupported(isBluetoothSupported());

      // Load saved printer info
      const savedInfo = getSavedPrinterInfo();
      if (savedInfo) {
        setPrinterInfo(savedInfo);
      }

      // Load saved discount settings
      const savedDiscountSettings = getDiscountSettings();
      setDiscountSettings(savedDiscountSettings);

      // Load saved payment summary settings
      const savedPaymentSummary = getPaymentSummary();
      setPaymentSummary(savedPaymentSummary);

      // Initialize tax categories
      initializeTaxCategories();

      // Load tax rates from API
      loadTaxRatesFromAPI();

      // Listen for tax categories changes
      const handleTaxCategoriesChanged = () => {
        console.log('Tax categories changed event received, refreshing tax rates...');
        loadTaxRatesFromAPI();
      };

      // Listen for customer orders loading
      const handleLoadCustomerOrders = (event) => {
        const { customer, orders } = event.detail;
        setSelectedCustomerForOrders(customer);
        setCustomerOrders(orders);
      };

      window.addEventListener('taxCategoriesChanged', handleTaxCategoriesChanged);
      window.addEventListener('loadCustomerOrders', handleLoadCustomerOrders);

      // Set up periodic sync for tax rates (every 5 minutes)
      const syncInterval = setInterval(() => {
        console.log('Performing periodic tax rates sync...');
        loadTaxRatesFromAPI();
      }, 5 * 60 * 1000); // 5 minutes

      // Cleanup event listener and interval
      return () => {
        window.removeEventListener('taxCategoriesChanged', handleTaxCategoriesChanged);
        window.removeEventListener('loadCustomerOrders', handleLoadCustomerOrders);
        clearInterval(syncInterval);
      };
    }, []); // eslint-disable-line react-hooks/exhaustive-deps

    // Function to calculate service charges based on company profile
    const calculateServiceChargesFromProfile = () => {
      if (!companyProfile || !companyProfile.result) {
        return 0;
      }

      const serviceChargePercentage = companyProfile.result.serviceCharge;

      // If serviceCharge is null, 0, or not provided, return 0
      if (!serviceChargePercentage || serviceChargePercentage === 0) {
        return 0;
      }

      // Calculate service charges based on subtotal
      const total = calculateOrderTotal({
        discountType: discountSettings.discountType,
        discountValue: discountSettings.discountValue,
        customPercentage: discountSettings.customPercentage,
        serviceCharges: 0, // Don't include service charges in subtotal calculation
        taxRate: paymentSummary.taxRate,
        voucher: paymentSummary.voucher
        // roundOff removed - now follows Invoice Settings automatically
      });

      // Calculate service charges: subtotal × percentage / 100
      const serviceCharges = (total.subtotal * serviceChargePercentage) / 100;
      return parseFloat(serviceCharges.toFixed(2));
    };

    // Update service charges when company profile changes
    useEffect(() => {
      if (companyProfile) {
        const calculatedServiceCharges = calculateServiceChargesFromProfile();

        // Only update if the calculated value is different from current
        if (calculatedServiceCharges !== paymentSummary.serviceCharges) {
          const newPaymentSummary = {
            ...paymentSummary,
            serviceCharges: calculatedServiceCharges
          };
          setPaymentSummary(newPaymentSummary);
          savePaymentSummary(newPaymentSummary);
        }
      }
    }, [companyProfile, discountSettings]);

    // Initialize order total and payment form
    useEffect(() => {
      const total = calculateOrderTotal({
        discountType: discountSettings.discountType,
        discountValue: discountSettings.discountValue,
        customPercentage: discountSettings.customPercentage,
        serviceCharges: paymentSummary.serviceCharges,
        taxRate: paymentSummary.taxRate,
        voucher: paymentSummary.voucher
        // roundOff removed - now follows Invoice Settings automatically
      });

      setOrderTotal(parseFloat(total.total));

      // Initialize payment form with remaining balance
      const balance = calculateRemainingBalance(parseFloat(total.total));
      setCashPaymentForm(prev => ({
        ...prev,
        payingAmount: balance.remaining.toString()
      }));
    }, [discountSettings, paymentSummary]);

  // Function to recalculate order total and trigger parent update
  const recalculateOrderTotal = (newDiscountSettings = discountSettings, newPaymentSummary = paymentSummary) => {
    const total = calculateOrderTotal({
      discountType: newDiscountSettings.discountType,
      discountValue: newDiscountSettings.discountValue,
      customPercentage: newDiscountSettings.customPercentage,
      serviceCharges: newPaymentSummary.serviceCharges,
      taxRate: newPaymentSummary.taxRate,
      voucher: newPaymentSummary.voucher,
      roundOff: newPaymentSummary.roundOff
    });

    // Update local order total
    setOrderTotal(parseFloat(total.total));

    // Trigger a custom event to notify the parent component
    window.dispatchEvent(new CustomEvent('orderTotalUpdated', {
      detail: {
        orderTotal: total,
        discountSettings: newDiscountSettings,
        paymentSummary: newPaymentSummary
      }
    }));
  };

  // Function to trigger payment update event
  const triggerPaymentUpdate = () => {
    const payments = getCurrentOrderPayments();
    window.dispatchEvent(new CustomEvent('paymentUpdated', {
      detail: { payments }
    }));
  };

  // Function to calculate change for cash payment
  const calculateChange = (receivedAmount, payingAmount) => {
    const received = parseFloat(receivedAmount) || 0;
    const paying = parseFloat(payingAmount) || 0;
    return Math.max(0, received - paying);
  };

  // Function to handle cash payment form changes
  const handleCashPaymentChange = (field, value) => {
    setCashPaymentForm(prev => {
      const updated = { ...prev, [field]: value };

      // Auto-calculate change when received amount or paying amount changes
      if (field === 'receivedAmount' || field === 'payingAmount') {
        const payingAmount = field === 'payingAmount' ? value : prev.payingAmount;
        const receivedAmount = field === 'receivedAmount' ? value : prev.receivedAmount;
        updated.change = calculateChange(receivedAmount, payingAmount);
      }

      return updated;
    });
  };

  // Function to handle quick cash selection
  const handleQuickCashSelect = (amount) => {
    const balance = calculateRemainingBalance(orderTotal);
    const payingAmount = Math.min(amount, balance.remaining);

    setCashPaymentForm(prev => ({
      ...prev,
      receivedAmount: amount.toString(),
      payingAmount: payingAmount.toString(),
      change: calculateChange(amount, payingAmount)
    }));
  };

  // Function to handle cash payment submission
  const handleCashPaymentSubmit = (e) => {
    e.preventDefault();

    const paymentData = {
      paymentMethod: 'cash',
      amount: parseFloat(cashPaymentForm.payingAmount) || 0,
      receivedAmount: parseFloat(cashPaymentForm.receivedAmount) || 0,
      change: cashPaymentForm.change,
      paymentType: cashPaymentForm.paymentType,
      paymentReceiver: cashPaymentForm.paymentReceiver,
      paymentNote: cashPaymentForm.paymentNote,
      saleNote: cashPaymentForm.saleNote,
      staffNote: cashPaymentForm.staffNote
    };

    // Add payment to storage
    addPaymentToCurrentOrder(paymentData);

    // Trigger payment update event
    triggerPaymentUpdate();

    // Reset form
    setCashPaymentForm({
      receivedAmount: '',
      payingAmount: '',
      change: 0,
      paymentType: 'cash',
      paymentReceiver: '',
      paymentNote: '',
      saleNote: '',
      staffNote: ''
    });

    // Close modal
    const modal = document.getElementById('payment-cash');
    if (modal) {
      const bootstrapModal = window.bootstrap?.Modal?.getInstance(modal);
      if (bootstrapModal) {
        bootstrapModal.hide();
      }
    }
  };

  const handleButtonClick = (value) => {
    setInput((prev) => prev + value);
  };

  const handleClear = () => {
    setInput("");
  };

  const handleBackspace = () => {
    setInput((prev) => prev.slice(0, -1));
  };

  const handleSolve = () => {
    try {
      // Safer evaluation - only allow basic math operations
      const sanitizedInput = input.replace(/[^0-9+\-*/.() ]/g, '');
      if (sanitizedInput && /^[0-9+\-*/.() ]+$/.test(sanitizedInput)) {
        // eslint-disable-next-line no-eval
        setInput(eval(sanitizedInput).toString());
      } else {
        setInput("Error");
      }
    } catch (error) {
      setInput("Error");
    }
  };

  const handleKeyPress = (event) => {
    if (/[0-9+\-*/%.]/.test(event.key)) {
      setInput((prev) => prev + event.key);
    } else if (event.key === "Backspace") {
      handleBackspace();
    } else if (event.key === "Enter") {
      handleSolve();
    } else if (event.key === "c") {
      handleClear();
    }
  };

  // Function to handle thermal receipt printing
  const handlePrintReceipt = async () => {
    try {
      setIsPrinting(true);

      // Extract receipt data from the DOM
      const receiptData = extractReceiptDataFromDOM(receiptRef.current);

      // Print the receipt
      const result = await printReceipt(receiptData);

      if (result.success) {
        // Update printer info state
        const savedInfo = getSavedPrinterInfo();
        if (savedInfo) {
          setPrinterInfo(savedInfo);
        }

        alert(`${result.message}\n\nIf your printer didn't print, please try the "Test Printer" button first to establish a connection.`);
      } else {
        alert(`Failed to print receipt: ${result.message}\n\nPlease try the "Test Printer" button to diagnose connection issues.`);
      }
    } catch (error) {
      console.error('Error printing receipt:', error);
      alert('An error occurred while printing the receipt.');
    } finally {
      setIsPrinting(false);
    }
  };

  // Function to test printer connection based on saved printer type
  // const handleTestPrinter = async () => {
  //   try {
  //     setIsPrinting(true);

  //     // Get saved printer info
  //     const savedInfo = getSavedPrinterInfo();

  //     // Determine which test function to call based on saved printer type
  //     if (savedInfo && savedInfo.connectionType === 'bluetooth') {
  //       await handleTestBluetoothPrinter();
  //     } else {
  //       await handleTestSerialPrinter();
  //     }
  //   } catch (error) {
  //     console.error('Error testing printer:', error);
  //     alert('An error occurred while testing the printer.');
  //   } finally {
  //     setIsPrinting(false);
  //   }
  // };

  // Function to connect to a Bluetooth printer
  const handleConnectBluetoothPrinter = async () => {
    try {
      setIsPrinting(true);

      // Discover and connect to a Bluetooth printer
      const result = await discoverBluetoothPrinter();

      if (result.success) {
        // Update printer info state
        const savedInfo = getSavedPrinterInfo();
        if (savedInfo) {
          setPrinterInfo(savedInfo);
        }

        alert(`${result.message}\n\nYour Bluetooth printer is now connected and ready to use.`);
      } else {
        alert(`Failed to connect to Bluetooth printer: ${result.message}`);
      }
    } catch (error) {
      console.error('Error connecting to Bluetooth printer:', error);
      alert('An error occurred while connecting to the Bluetooth printer.');
    } finally {
      setIsPrinting(false);
    }
  };

  // Function to test Serial printer
  const handleTestSerialPrinter = async () => {
    try {
      setIsPrinting(true);

      // Test Serial printer connection
      const result = await testPrinterConnection('serial');

      if (result.success) {
        // Update printer info state
        const savedInfo = getSavedPrinterInfo();
        if (savedInfo) {
          setPrinterInfo(savedInfo);
        }

        alert(`${result.message}\n\nA test page should have printed. If not, please check your printer power and connection.`);
      } else {
        alert(`Printer test failed: ${result.message}`);
      }
    } catch (error) {
      console.error('Error testing printer:', error);
      alert('An error occurred while testing the printer.');
    } finally {
      setIsPrinting(false);
    }
  };

  // Function to test Bluetooth printer
  const handleTestBluetoothPrinter = async () => {
    try {
      setIsPrinting(true);

      // Test Bluetooth printer connection
      const result = await testPrinterConnection('bluetooth');

      if (result.success) {
        // Update printer info state
        const savedInfo = getSavedPrinterInfo();
        if (savedInfo) {
          setPrinterInfo(savedInfo);
        }

        alert(`${result.message}\n\nA test page should have printed. If not, please check your printer power and connection.`);
      } else {
        alert(`Bluetooth printer test failed: ${result.message}`);
      }
    } catch (error) {
      console.error('Error testing Bluetooth printer:', error);
      alert('An error occurred while testing the Bluetooth printer.');
    } finally {
      setIsPrinting(false);
    }
  };

  // Function to reset printer settings
  const handleResetPrinter = () => {
    if (window.confirm('Are you sure you want to reset the printer settings? You will need to select the printer again next time you print.')) {
      clearSavedPrinterInfo();
      setPrinterInfo(null);
      alert('Printer settings have been reset.');
    }
  };

  // Customer form handling functions
  const handleCustomerFormChange = (field, value) => {
    setCustomerForm(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error for this field when user starts typing
    if (customerFormErrors[field]) {
      setCustomerFormErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateCustomerForm = () => {
    const errors = {};

    if (!customerForm.name.trim()) {
      errors.name = 'Customer name is required';
    }

    if (!customerForm.phone.trim()) {
      errors.phone = 'Phone number is required';
    }

    if (customerForm.email && !/\S+@\S+\.\S+/.test(customerForm.email)) {
      errors.email = 'Please enter a valid email address';
    }

    // Validate TIN number format if provided
    if (customerForm.tinNo && !/^[\d\-A-Z]+$/.test(customerForm.tinNo)) {
      errors.tinNo = 'Please enter a valid TIN number';
    }

    // Validate identity number if provided
    if (customerForm.identityNo && customerForm.identityNo.length < 6) {
      errors.identityNo = 'Identity number must be at least 6 characters';
    }

    return errors;
  };

  const handleCustomerFormSubmit = async (e) => {
    e.preventDefault();

    // Validate form
    const errors = validateCustomerForm();
    if (Object.keys(errors).length > 0) {
      setCustomerFormErrors(errors);
      return;
    }

    try {
      setIsCreatingCustomer(true);

      // Create customer with all backend fields
      const newCustomer = await addCustomer({
        code: customerForm.code.trim(),
        name: customerForm.name.trim(),
        description: customerForm.description.trim(),
        isTaxExempt: customerForm.isTaxExempt,
        isPICEditable: customerForm.isPICEditable,
        defaultPIC: customerForm.defaultPIC.trim(),
        accountCode: customerForm.accountCode.trim(),
        remark: customerForm.remark.trim(),
        identityTypeId: customerForm.identityTypeId,
        identityNo: customerForm.identityNo.trim(),
        fullName: customerForm.fullName.trim() || customerForm.name.trim(),
        email: customerForm.email.trim(),
        tinNo: customerForm.tinNo.trim(),
        tinVerifyStatus: customerForm.tinVerifyStatus,
        debtorTypeId: customerForm.debtorTypeId,
        referrerId: customerForm.referrerId,
        currencyId: customerForm.currencyId,
        accountGroupId: customerForm.accountGroupId,
        companyId: customerForm.companyId,
        // Address fields
        firstName: customerForm.firstName.trim(),
        lastName: customerForm.lastName.trim(),
        address1: customerForm.address1.trim(),
        address2: customerForm.address2.trim(),
        address3: customerForm.address3.trim(),
        postalCode: customerForm.postalCode.trim(),
        phone: customerForm.phone.trim(),
        faxNo: customerForm.faxNo.trim(),
        coordinate: customerForm.coordinate.trim(),
        countryId: customerForm.countryId,
        stateProvinceId: customerForm.stateProvinceId,
        regionId: customerForm.regionId
      });

      // Set as selected customer
      setSelectedCustomer(newCustomer);

      // Reset form
      setCustomerForm({
        code: '',
        name: '',
        description: '',
        isTaxExempt: false,
        isPICEditable: true,
        defaultPIC: '',
        accountCode: '',
        remark: '',
        identityTypeId: null,
        identityNo: '',
        fullName: '',
        email: '',
        tinNo: '',
        tinVerifyStatus: 0,
        debtorTypeId: null,
        referrerId: null,
        currencyId: null,
        accountGroupId: null,
        companyId: null,
        // Address fields
        firstName: '',
        lastName: '',
        address1: '',
        address2: '',
        address3: '',
        postalCode: '',
        phone: '',
        faxNo: '',
        coordinate: '',
        countryId: null,
        stateProvinceId: null,
        regionId: null
      });
      setCustomerFormErrors({});

      // Trigger customer update event for parent components
      window.dispatchEvent(new CustomEvent('customerUpdated', {
        detail: { customer: newCustomer }
      }));

      // Close modal
      const modal = document.getElementById('create');
      if (modal) {
        const bootstrapModal = window.bootstrap?.Modal?.getInstance(modal);
        if (bootstrapModal) {
          bootstrapModal.hide();
        }
      }

      alert('Customer created successfully!');

    } catch (error) {
      console.error('Error creating customer:', error);
      alert('Failed to create customer. Please try again.');
    } finally {
      setIsCreatingCustomer(false);
    }
  };

  // Order saving functions
  const handleSaveOrderForCustomer = async () => {
    try {
      setIsSavingOrder(true);

      // Get current order data
      const currentOrders = getOrders();
      const currentDiscountSettings = getOrderDiscountSettings();
      const currentPaymentSummary = getOrderPaymentSummary();
      const currentPayments = getCurrentOrderPayments();

      // Get selected customer from localStorage
      const selectedCustomer = JSON.parse(localStorage.getItem('pos_selected_customer') || 'null');

      if (!selectedCustomer || selectedCustomer.id === 'walk-in') {
        alert('Please select a customer before saving the order.');
        return;
      }

      if (!currentOrders || currentOrders.length === 0) {
        alert('No items in the order to save.');
        return;
      }

      // Calculate order total
      const orderTotal = calculateOrderTotal({
        discountType: currentDiscountSettings.discountType,
        discountValue: currentDiscountSettings.discountValue,
        customPercentage: currentDiscountSettings.customPercentage,
        serviceCharges: currentPaymentSummary.serviceCharges,
        taxRate: currentPaymentSummary.taxRate,
        voucher: currentPaymentSummary.voucher,
        roundOff: currentPaymentSummary.roundOff
      });

      // Prepare order data
      const orderData = {
        customerId: selectedCustomer.id,
        customerName: selectedCustomer.name,
        orderItems: currentOrders,
        orderTotal: orderTotal,
        discountSettings: currentDiscountSettings,
        paymentSummary: currentPaymentSummary,
        payments: currentPayments,
        status: 'saved',
        notes: orderNotes
      };

      // Save order
      const savedOrder = await saveOrderForCustomer(orderData);

      // Clear current order
      clearOrders();

      // Trigger order update events
      window.dispatchEvent(new CustomEvent('orderCleared'));
      window.dispatchEvent(new CustomEvent('orderSaved', {
        detail: { order: savedOrder, customer: selectedCustomer }
      }));

      // Reset notes
      setOrderNotes('');

      // Close modal
      const modal = document.getElementById('save-order');
      if (modal) {
        const bootstrapModal = window.bootstrap?.Modal?.getInstance(modal);
        if (bootstrapModal) {
          bootstrapModal.hide();
        }
      }

      alert(`Order saved successfully for ${selectedCustomer.name}!\nOrder Reference: ${savedOrder.reference}`);

    } catch (error) {
      console.error('Error saving order:', error);
      alert('Failed to save order. Please try again.');
    } finally {
      setIsSavingOrder(false);
    }
  };

  const loadCustomerOrders = (customerId) => {
    try {
      const orders = getOrdersForCustomer(customerId);
      setCustomerOrders(orders);
    } catch (error) {
      console.error('Error loading customer orders:', error);
      setCustomerOrders([]);
    }
  };

  const handleLoadSavedOrder = async (orderId) => {
    try {
      const savedOrder = loadSavedOrder(orderId);

      if (!savedOrder) {
        alert('Order not found.');
        return;
      }

      // Clear current order first
      clearOrders();

      // Load order data into current order
      if (savedOrder.orderItems && savedOrder.orderItems.length > 0) {
        // Save order items to current order storage
        localStorage.setItem('pos_orders', JSON.stringify(savedOrder.orderItems));
      }

      // Load discount settings
      if (savedOrder.discountSettings) {
        saveDiscountSettings(savedOrder.discountSettings);
        setDiscountSettings(savedOrder.discountSettings);
      }

      // Load payment summary
      if (savedOrder.paymentSummary) {
        savePaymentSummary(savedOrder.paymentSummary);
        setPaymentSummary(savedOrder.paymentSummary);
      }

      // Set customer as selected
      if (savedOrder.customerId) {
        const customer = {
          id: savedOrder.customerId,
          name: savedOrder.customerName
        };
        setSelectedCustomer(customer);
      }

      // Trigger order update events
      window.dispatchEvent(new CustomEvent('orderLoaded', {
        detail: { order: savedOrder }
      }));

      // Close modal
      const modal = document.getElementById('customer-orders');
      if (modal) {
        const bootstrapModal = window.bootstrap?.Modal?.getInstance(modal);
        if (bootstrapModal) {
          bootstrapModal.hide();
        }
      }

      alert(`Order loaded successfully!\nReference: ${savedOrder.reference}`);

    } catch (error) {
      console.error('Error loading saved order:', error);
      alert('Failed to load order. Please try again.');
    }
  };

  const handleDeleteSavedOrder = async (orderId, orderReference) => {
    if (!window.confirm(`Are you sure you want to delete order ${orderReference}?`)) {
      return;
    }

    try {
      const success = deleteSavedOrder(orderId);

      if (success) {
        // Reload customer orders
        if (selectedCustomerForOrders) {
          loadCustomerOrders(selectedCustomerForOrders.id);
        }
        alert('Order deleted successfully.');
      } else {
        alert('Failed to delete order.');
      }
    } catch (error) {
      console.error('Error deleting order:', error);
      alert('Failed to delete order. Please try again.');
    }
  };
  const options = {
    taxType: [
      { value: "exclusive", label: "Exclusive" },
      { value: "inclusive", label: "Inclusive" },
    ],
    discountType: [
      { value: "percentage", label: "Percentage" },
      { value: "early_payment", label: "Early payment discounts" },
    ],
    weightUnits: [
      { value: "kg", label: "Kilogram" },
      { value: "g", label: "Grams" },
    ],
    taxRates: taxRatesFromAPI.length > 0 ? taxRatesFromAPI : getTaxCategoriesForSelect(),
    // New tax categories for the updated tax selection modal
    taxCategories: taxCategories,
    couponCodes: [
      { value: "select", label: "Select" },
      { value: "newyear30", label: "NEWYEAR30" },
      { value: "christmas100", label: "CHRISTMAS100" },
      { value: "halloween20", label: "HALLOWEEN20" },
      { value: "blackfriday50", label: "BLACKFRIDAY50" },
    ],
    discountMode: [
      { value: "select", label: "Select" },
      { value: "flat", label: "Flat" },
      { value: "percentage", label: "Percentage" },
    ],
    paymentMethods: [
      { value: "cash", label: "Cash" },
      { value: "card", label: "Card" },
    ],
    paymentTypes: [
      { value: "credit", label: "Credit Card" },
      { value: "cash", label: "Cash" },
      { value: "cheque", label: "Cheque" },
      { value: "deposit", label: "Deposit" },
      { value: "points", label: "Points" },
    ],
  };

  return (
    <>
      {/* Custom CSS to override global React Select styles */}
      <style>
        {`
          /* Override global CSS rules for tax rate select dropdown */
          div.tax-rate-select .react-select__option--is-focused:not(.react-select__option--is-selected) {
            background-color: #ffffff !important;
            color: #333333 !important;
          }
          div.tax-rate-select .react-select__option--is-selected {
            background-color: #fd7e14 !important;
            color: #ffffff !important;
          }
          div.tax-rate-select .react-select__option--is-selected.react-select__option--is-focused {
            background-color: #fd7e14 !important;
            color: #ffffff !important;
          }
          div.tax-rate-select .react-select__option:hover:not(.react-select__option--is-selected) {
            background-color: #f8f9fa !important;
            color: #333333 !important;
          }
          div.tax-rate-select .react-select__option--is-selected:hover {
            background-color: #e8681a !important;
            color: #ffffff !important;
          }
          /* Override the specific global CSS rule that was causing the issue */
          div.tax-rate-select.css-b62m3t-container .react-select__option--is-focused:not(.react-select__option--is-selected) {
            background-color: #ffffff !important;
            color: #333333 !important;
          }
        `}
      </style>
  {/* Payment Completed */}
  <div
    className="modal fade modal-default"
    id="payment-completed"
    aria-labelledby="payment-completed"
  >
    <div className="modal-dialog modal-dialog-centered">
      <div className="modal-content">
        <div className="modal-body p-0">
          <div className="success-wrap text-center">
            <form >
              <div className="icon-success bg-success text-white mb-2">
                <i className="ti ti-check" />
              </div>
              <h3 className="mb-2">Payment Completed</h3>
              <p className="mb-3">
                Do you want to Print Receipt for the Completed Order
              </p>
              <div className="d-flex align-items-center justify-content-center gap-2 flex-wrap">
                <button
                  type="button"
                  className="btn btn-md btn-secondary"
                  data-bs-toggle="modal"
                  data-bs-target="#print-receipt"
                >
                  Print Receipt
                  <i className="feather-arrow-right-circle icon-me-5" />
                </button>
                <button type="button" data-bs-dismiss="modal" className="btn btn-md btn-primary">
                  Next Order
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
  {/* /Payment Completed */}
  {/* Print Receipt */}
  <div
    className="modal fade modal-default"
    id="print-receipt"
    aria-labelledby="print-receipt"
  >
    <div className="modal-dialog modal-dialog-centered">
      <div className="modal-content">
        <div className="modal-body" ref={receiptRef}>
          <div className="icon-head text-center">
            <Link to="#">
              <ImageWithBasePath
                src="assets/img/logo.png"
                width={100}
                height={30}
                alt="Receipt Logo"
              />
            </Link>
          </div>
          <div className="text-center info text-center">
            <h6>Dreamguys Technologies Pvt Ltd.,</h6>
            <p className="mb-0">Phone Number: *************</p>
            <p className="mb-0">
              Email: <Link to="mailto:<EMAIL>"><EMAIL></Link>
            </p>
          </div>
          <div className="tax-invoice">
            <h6 className="text-center">Tax Invoice</h6>
            <div className="row">
              <div className="col-sm-12 col-md-6">
                <div className="invoice-user-name">
                  <span>Name: </span>John Doe
                </div>
                <div className="invoice-user-name">
                  <span>Invoice No: </span>CS132453
                </div>
              </div>
              <div className="col-sm-12 col-md-6">
                <div className="invoice-user-name">
                  <span>Customer Id: </span>#LL93784
                </div>
                <div className="invoice-user-name">
                  <span>Date: </span>01.07.2022
                </div>
              </div>
            </div>
          </div>
          <table className="table-borderless w-100 table-fit">
            <thead>
              <tr>
                <th># Item</th>
                <th>Price</th>
                <th>Qty</th>
                <th className="text-end">Total</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>1. Red Nike Laser</td>
                <td>$50</td>
                <td>3</td>
                <td className="text-end">$150</td>
              </tr>
              <tr>
                <td>2. Iphone 14</td>
                <td>$50</td>
                <td>2</td>
                <td className="text-end">$100</td>
              </tr>
              <tr>
                <td>3. Apple Series 8</td>
                <td>$50</td>
                <td>3</td>
                <td className="text-end">$150</td>
              </tr>
              <tr>
                <td colSpan={4}>
                  <table className="table-borderless w-100 table-fit">
                    <tbody>
                      <tr>
                        <td className="fw-bold">Sub Total :</td>
                        <td className="text-end">$700.00</td>
                      </tr>
                      <tr>
                        <td className="fw-bold">Discount :</td>
                        <td className="text-end">-$50.00</td>
                      </tr>
                      <tr>
                        <td className="fw-bold">Shipping :</td>
                        <td className="text-end">0.00</td>
                      </tr>
                      <tr>
                        <td className="fw-bold">Tax (5%) :</td>
                        <td className="text-end">$5.00</td>
                      </tr>
                      <tr>
                        <td className="fw-bold">Total Bill :</td>
                        <td className="text-end">$655.00</td>
                      </tr>
                      <tr>
                        <td className="fw-bold">Due :</td>
                        <td className="text-end">$0.00</td>
                      </tr>
                      <tr>
                        <td className="fw-bold">Total Payable :</td>
                        <td className="text-end">$655.00</td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </tbody>
          </table>
          <div className="text-center invoice-bar">
            <div className="border-bottom border-dashed">
              <p>
                **VAT against this challan is payable through central
                registration. Thank you for your business!
              </p>
            </div>
            <Link to="#">
              <ImageWithBasePath src="assets/img/barcode/barcode-03.jpg" alt="Barcode" />
            </Link>
            <p className="text-dark fw-bold">Sale 31</p>
            <p>Thank You For Shopping With Us. Please Come Again</p>
            {printerInfo && (
              <div className="mb-3 text-center">
                <p className="mb-1 text-success">
                  <i className="ti ti-printer me-1"></i>
                  {printerInfo.connectionType === 'bluetooth' ? 'Bluetooth' : 'USB'} Printer connected
                </p>
                <small className="text-muted">
                  {printerInfo.deviceName || printerInfo.connectionType === 'bluetooth' ? 'Bluetooth Device' : 'USB Device'}
                  <br />
                  Last used: {new Date(printerInfo.lastUsed).toLocaleString()}
                </small>
              </div>
            )}

            <div className="d-flex gap-2 justify-content-center">
              <button
                onClick={handlePrintReceipt}
                className="btn btn-md btn-primary"
                disabled={isPrinting}
              >
                {isPrinting ? 'Printing...' : 'Print Receipt'}
              </button>
            </div>

            <div className="mt-3">
              <p className="text-center mb-2 fw-bold">Printer Options</p>
              <div className="d-flex gap-2 justify-content-center flex-wrap">
                <button
                  onClick={handleTestSerialPrinter}
                  className="btn btn-sm btn-outline-secondary"
                  disabled={isPrinting}
                >
                  <i className="ti ti-usb me-1"></i>
                  Test USB Printer
                </button>

                {bluetoothSupported && (
                  <button
                    onClick={handleTestBluetoothPrinter}
                    className="btn btn-sm btn-outline-info"
                    disabled={isPrinting}
                  >
                    <i className="ti ti-bluetooth me-1"></i>
                    Test Bluetooth Printer
                  </button>
                )}

                {bluetoothSupported && (
                  <button
                    onClick={handleConnectBluetoothPrinter}
                    className="btn btn-sm btn-outline-primary"
                    disabled={isPrinting}
                  >
                    <i className="ti ti-bluetooth-connected me-1"></i>
                    Connect Bluetooth
                  </button>
                )}
              </div>

              {printerInfo && (
                <div className="mt-2 text-center">
                  <button
                    onClick={handleResetPrinter}
                    className="btn btn-sm btn-link text-danger"
                    disabled={isPrinting}
                  >
                    Reset Printer Settings
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  {/* /Print Receipt */}
  {/* Products */}
  <div
    className="modal fade modal-default pos-modal"
    id="products"
    aria-labelledby="products"
  >
    <div className="modal-dialog modal-dialog-centered">
      <div className="modal-content">
        <div className="modal-header d-flex align-items-center justify-content-between">
          <div className="d-flex align-items-center">
            <h5 className="me-4">Products</h5>
          </div>
          <button
            type="button"
            className="close"
            data-bs-dismiss="modal"
            aria-label="Close"
          >
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <div className="modal-body">
          <div className="card bg-light mb-3">
            <div className="card-body">
              <div className="d-flex align-items-center justify-content-between gap-3 flex-wrap mb-3">
                <span className="badge bg-dark fs-12">Order ID : #45698</span>
                <p className="fs-16">Number of Products : 02</p>
              </div>
              <div className="product-wrap h-auto">
                <div className="product-list bg-white align-items-center justify-content-between">
                  <div
                    className="d-flex align-items-center product-info"
                    data-bs-toggle="modal"
                    data-bs-target="#products"
                  >
                    <Link to="#" className="pro-img">
                      <ImageWithBasePath
                        src="assets/img/products/pos-product-16.png"
                        alt="Products"
                      />
                    </Link>
                    <div className="info">
                      <h6>
                        <Link to="#">Red Nike Laser</Link>
                      </h6>
                      <p>Quantity : 04</p>
                    </div>
                  </div>
                  <p className="text-teal fw-bold">$2000</p>
                </div>
                <div className="product-list bg-white align-items-center justify-content-between">
                  <div
                    className="d-flex align-items-center product-info"
                    data-bs-toggle="modal"
                    data-bs-target="#products"
                  >
                    <Link to="#" className="pro-img">
                      <ImageWithBasePath
                        src="assets/img/products/pos-product-17.png"
                        alt="Products"
                      />
                    </Link>
                    <div className="info">
                      <h6>
                        <Link to="#">Iphone 11S</Link>
                      </h6>
                      <p>Quantity : 04</p>
                    </div>
                  </div>
                  <p className="text-teal fw-bold">$3000</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  {/* /Products */}
  <div
    className="modal fade"
    id="create"
    tabIndex={-1}
    aria-labelledby="create"
    aria-hidden="true"
  >
    <div
      className="modal-dialog modal-lg modal-dialog-centered"
      role="document"
    >
      <div className="modal-content">
        <div className="modal-header">
          <h5 className="modal-title">Create Customer</h5>
          <button
            type="button"
            className="close"
            data-bs-dismiss="modal"
            aria-label="Close"
          >
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <form onSubmit={handleCustomerFormSubmit}>
          <div className="modal-body pb-1">
            <div className="row">
              {/* Basic Information */}
              <div className="col-12">
                <h6 className="mb-3 text-primary">Basic Information</h6>
              </div>
              <div className="col-lg-6 col-sm-12 col-12">
                <div className="mb-3">
                  <label className="form-label">Customer Code</label>
                  <input
                    type="text"
                    className="form-control"
                    value={customerForm.code}
                    onChange={(e) => handleCustomerFormChange('code', e.target.value)}
                    placeholder="Enter customer code (optional)"
                  />
                </div>
              </div>
              <div className="col-lg-6 col-sm-12 col-12">
                <div className="mb-3">
                  <label className="form-label">
                    Customer Name <span className="text-danger">*</span>
                  </label>
                  <input
                    type="text"
                    className={`form-control ${customerFormErrors.name ? 'is-invalid' : ''}`}
                    value={customerForm.name}
                    onChange={(e) => handleCustomerFormChange('name', e.target.value)}
                    placeholder="Enter customer name"
                  />
                  {customerFormErrors.name && (
                    <div className="invalid-feedback">{customerFormErrors.name}</div>
                  )}
                </div>
              </div>
              <div className="col-lg-6 col-sm-12 col-12">
                <div className="mb-3">
                  <label className="form-label">Full Name</label>
                  <input
                    type="text"
                    className="form-control"
                    value={customerForm.fullName}
                    onChange={(e) => handleCustomerFormChange('fullName', e.target.value)}
                    placeholder="Enter full name (optional)"
                  />
                </div>
              </div>
              <div className="col-lg-6 col-sm-12 col-12">
                <div className="mb-3">
                  <label className="form-label">Description</label>
                  <input
                    type="text"
                    className="form-control"
                    value={customerForm.description}
                    onChange={(e) => handleCustomerFormChange('description', e.target.value)}
                    placeholder="Enter description (optional)"
                  />
                </div>
              </div>

              {/* Contact Information */}
              <div className="col-12">
                <h6 className="mb-3 text-primary">Contact Information</h6>
              </div>
              <div className="col-lg-6 col-sm-12 col-12">
                <div className="mb-3">
                  <label className="form-label">
                    Phone <span className="text-danger">*</span>
                  </label>
                  <input
                    type="text"
                    className={`form-control ${customerFormErrors.phone ? 'is-invalid' : ''}`}
                    value={customerForm.phone}
                    onChange={(e) => handleCustomerFormChange('phone', e.target.value)}
                    placeholder="Enter phone number"
                  />
                  {customerFormErrors.phone && (
                    <div className="invalid-feedback">{customerFormErrors.phone}</div>
                  )}
                </div>
              </div>
              <div className="col-lg-6 col-sm-12 col-12">
                <div className="mb-3">
                  <label className="form-label">Email</label>
                  <input
                    type="email"
                    className={`form-control ${customerFormErrors.email ? 'is-invalid' : ''}`}
                    value={customerForm.email}
                    onChange={(e) => handleCustomerFormChange('email', e.target.value)}
                    placeholder="Enter email address (optional)"
                  />
                  {customerFormErrors.email && (
                    <div className="invalid-feedback">{customerFormErrors.email}</div>
                  )}
                </div>
              </div>
              <div className="col-lg-6 col-sm-12 col-12">
                <div className="mb-3">
                  <label className="form-label">Fax Number</label>
                  <input
                    type="text"
                    className="form-control"
                    value={customerForm.faxNo}
                    onChange={(e) => handleCustomerFormChange('faxNo', e.target.value)}
                    placeholder="Enter fax number (optional)"
                  />
                </div>
              </div>

              {/* Address Information */}
              <div className="col-12">
                <h6 className="mb-3 text-primary">Address Information</h6>
              </div>
              <div className="col-lg-6 col-sm-12 col-12">
                <div className="mb-3">
                  <label className="form-label">First Name</label>
                  <input
                    type="text"
                    className="form-control"
                    value={customerForm.firstName}
                    onChange={(e) => handleCustomerFormChange('firstName', e.target.value)}
                    placeholder="Enter first name (optional)"
                  />
                </div>
              </div>
              <div className="col-lg-6 col-sm-12 col-12">
                <div className="mb-3">
                  <label className="form-label">Last Name</label>
                  <input
                    type="text"
                    className="form-control"
                    value={customerForm.lastName}
                    onChange={(e) => handleCustomerFormChange('lastName', e.target.value)}
                    placeholder="Enter last name (optional)"
                  />
                </div>
              </div>
              <div className="col-lg-12">
                <div className="mb-3">
                  <label className="form-label">Address Line 1</label>
                  <input
                    type="text"
                    className="form-control"
                    value={customerForm.address1}
                    onChange={(e) => handleCustomerFormChange('address1', e.target.value)}
                    placeholder="Enter address line 1 (optional)"
                  />
                </div>
              </div>
              <div className="col-lg-6 col-sm-12 col-12">
                <div className="mb-3">
                  <label className="form-label">Address Line 2</label>
                  <input
                    type="text"
                    className="form-control"
                    value={customerForm.address2}
                    onChange={(e) => handleCustomerFormChange('address2', e.target.value)}
                    placeholder="Enter address line 2 (optional)"
                  />
                </div>
              </div>
              <div className="col-lg-6 col-sm-12 col-12">
                <div className="mb-3">
                  <label className="form-label">Address Line 3</label>
                  <input
                    type="text"
                    className="form-control"
                    value={customerForm.address3}
                    onChange={(e) => handleCustomerFormChange('address3', e.target.value)}
                    placeholder="Enter address line 3 (optional)"
                  />
                </div>
              </div>
              <div className="col-lg-6 col-sm-12 col-12">
                <div className="mb-3">
                  <label className="form-label">Postal Code</label>
                  <input
                    type="text"
                    className="form-control"
                    value={customerForm.postalCode}
                    onChange={(e) => handleCustomerFormChange('postalCode', e.target.value)}
                    placeholder="Enter postal code (optional)"
                  />
                </div>
              </div>

              {/* Tax & Business Information */}
              <div className="col-12">
                <h6 className="mb-3 text-primary">Tax & Business Information</h6>
              </div>
              <div className="col-lg-6 col-sm-12 col-12">
                <div className="mb-3">
                  <label className="form-label">TIN Number</label>
                  <input
                    type="text"
                    className={`form-control ${customerFormErrors.tinNo ? 'is-invalid' : ''}`}
                    value={customerForm.tinNo}
                    onChange={(e) => handleCustomerFormChange('tinNo', e.target.value)}
                    placeholder="Enter TIN number (optional)"
                  />
                  {customerFormErrors.tinNo && (
                    <div className="invalid-feedback">{customerFormErrors.tinNo}</div>
                  )}
                </div>
              </div>
              <div className="col-lg-6 col-sm-12 col-12">
                <div className="mb-3">
                  <label className="form-label">Identity Number</label>
                  <input
                    type="text"
                    className={`form-control ${customerFormErrors.identityNo ? 'is-invalid' : ''}`}
                    value={customerForm.identityNo}
                    onChange={(e) => handleCustomerFormChange('identityNo', e.target.value)}
                    placeholder="Enter identity number (optional)"
                  />
                  {customerFormErrors.identityNo && (
                    <div className="invalid-feedback">{customerFormErrors.identityNo}</div>
                  )}
                </div>
              </div>
              <div className="col-lg-6 col-sm-12 col-12">
                <div className="mb-3">
                  <label className="form-label">Account Code</label>
                  <input
                    type="text"
                    className="form-control"
                    value={customerForm.accountCode}
                    onChange={(e) => handleCustomerFormChange('accountCode', e.target.value)}
                    placeholder="Enter account code (optional)"
                  />
                </div>
              </div>
              <div className="col-lg-6 col-sm-12 col-12">
                <div className="mb-3">
                  <label className="form-label">Default PIC</label>
                  <input
                    type="text"
                    className="form-control"
                    value={customerForm.defaultPIC}
                    onChange={(e) => handleCustomerFormChange('defaultPIC', e.target.value)}
                    placeholder="Enter default PIC (optional)"
                  />
                </div>
              </div>
              <div className="col-lg-12">
                <div className="mb-3">
                  <label className="form-label">Remarks</label>
                  <textarea
                    className="form-control"
                    rows="3"
                    value={customerForm.remark}
                    onChange={(e) => handleCustomerFormChange('remark', e.target.value)}
                    placeholder="Enter remarks (optional)"
                  />
                </div>
              </div>

              {/* Tax Settings */}
              <div className="col-12">
                <h6 className="mb-3 text-primary">Tax Settings</h6>
              </div>
              <div className="col-lg-6 col-sm-12 col-12">
                <div className="mb-3">
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      id="isTaxExempt"
                      checked={customerForm.isTaxExempt}
                      onChange={(e) => handleCustomerFormChange('isTaxExempt', e.target.checked)}
                    />
                    <label className="form-check-label" htmlFor="isTaxExempt">
                      Tax Exempt Customer
                    </label>
                  </div>
                </div>
              </div>
              <div className="col-lg-6 col-sm-12 col-12">
                <div className="mb-3">
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      id="isPICEditable"
                      checked={customerForm.isPICEditable}
                      onChange={(e) => handleCustomerFormChange('isPICEditable', e.target.checked)}
                    />
                    <label className="form-check-label" htmlFor="isPICEditable">
                      PIC Editable
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="modal-footer d-flex justify-content-end gap-2 flex-wrap">
            <button
              type="button"
              className="btn btn-md btn-secondary"
              data-bs-dismiss="modal"
              disabled={isCreatingCustomer}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn-md btn-primary"
              disabled={isCreatingCustomer}
            >
              {isCreatingCustomer ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Creating...
                </>
              ) : (
                'Create Customer'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
  {/* Hold */}
  <div
    className="modal fade modal-default pos-modal"
    id="hold-order"
    aria-labelledby="hold-order"
  >
    <div className="modal-dialog modal-dialog-centered">
      <div className="modal-content">
        <div className="modal-header">
          <h5 className="modal-title">Hold order</h5>
          <button
            type="button"
            className="close"
            data-bs-dismiss="modal"
            aria-label="Close"
          >
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <form >
          <div className="modal-body">
            <div className="bg-light br-10 p-4 text-center mb-3">
              <h2 className="display-1">4500.00</h2>
            </div>
            <div className="mb-3">
              <label className="form-label">
                Order Reference <span className="text-danger">*</span>
              </label>
              <input
                className="form-control"
                type="text"
                defaultValue=""
                placeholder=""
              />
            </div>
            <p>
              The current order will be set on hold. You can retreive this order
              from the pending order button. Providing a reference to it might
              help you to identify the order more quickly.
            </p>
          </div>
          <div className="modal-footer d-flex justify-content-end gap-2">
            <button
              type="button"
              className="btn btn-md btn-secondary"
              data-bs-dismiss="modal"
            >
              Cancel
            </button>
            <button type="button" data-bs-dismiss="modal" className="btn btn-md btn-primary">
              Confirm
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
  {/* /Hold */}
  {/* Edit Product */}
  <div
    className="modal fade modal-default pos-modal"
    id="edit-product"
    aria-labelledby="edit-product"
  >
    <div className="modal-dialog modal-dialog-centered">
      <div className="modal-content">
        <div className="modal-header">
          <h5 className="modal-title">Edit Product</h5>
          <button
            type="button"
            className="close"
            data-bs-dismiss="modal"
            aria-label="Close"
          >
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <form >
          <div className="modal-body pb-1">
            <div className="row">
              <div className="col-lg-12">
                <div className="mb-3">
                  <label className="form-label">
                    Product Name <span className="text-danger">*</span>
                  </label>
                  <input
                    type="text"
                    className="form-control"
                    defaultValue="Red Nike Laser Show"
                    disabled=""
                  />
                </div>
              </div>
              <div className="col-lg-6 col-sm-12 col-12">
                <div className="mb-3">
                  <label className="form-label">
                    Product Price <span className="text-danger">*</span>
                  </label>
                  <div className="input-icon-start position-relative">
                    <span className="input-icon-addon text-gray-9">
                      <i className="ti ti-currency-dollar" />
                    </span>
                    <input
                      type="text"
                      className="form-control"
                      defaultValue={1800}
                    />
                  </div>
                </div>
              </div>
              <div className="col-lg-6 col-sm-12 col-12">
                <div className="mb-3">
                  <label className="form-label">
                    Tax Type <span className="text-danger">*</span>
                  </label>

                  <Select
                    options={options.taxType}
                    classNamePrefix="react-select select"
                    placeholder="Select"
                    defaultValue={options.taxType[0]}
                    />
                </div>
              </div>
              <div className="col-lg-6 col-sm-12 col-12">
                <div className="mb-3">
                  <label className="form-label">
                    Tax <span className="text-danger">*</span>
                  </label>
                  <div className="input-icon-start position-relative">
                    <span className="input-icon-addon text-gray-9">
                      <i className="ti ti-percentage" />
                    </span>
                    <input
                      type="text"
                      className="form-control"
                      defaultValue={15}
                    />
                  </div>
                </div>
              </div>
              <div className="col-lg-6 col-sm-12 col-12">
                <div className="mb-3">
                  <label className="form-label">
                    Discount Type <span className="text-danger">*</span>
                  </label>

                  <Select
                    options={options.discountType}
                    classNamePrefix="react-select select"
                    placeholder="Select"
                    />
                </div>
              </div>
              <div className="col-lg-6 col-sm-12 col-12">
                <div className="mb-3">
                  <label className="form-label">
                    Discount <span className="text-danger">*</span>
                  </label>
                  <input
                    type="text"
                    className="form-control"
                    defaultValue={15}
                  />
                </div>
              </div>
              <div className="col-lg-6 col-sm-12 col-12">
                <div className="mb-3">
                  <label className="form-label">
                    Sale Unit <span className="text-danger">*</span>
                  </label>
                  <Select
                    options={options.weightUnits}
                    classNamePrefix="react-select select"
                    placeholder="Select"
                    />
                </div>
              </div>
            </div>
          </div>
          <div className="modal-footer d-flex justify-content-end flex-wrap gap-2">
            <button
              type="button"
              className="btn btn-md btn-secondary"
              data-bs-dismiss="modal"
            >
              Cancel
            </button>
            <button type="button" data-bs-dismiss="modal" className="btn btn-md btn-primary">
              Submit
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
  {/* /Edit Product */}
  {/* Delete Product */}
  <div
    className="modal fade modal-default"
    id="delete"
    aria-labelledby="payment-completed"
  >
    <div className="modal-dialog modal-dialog-centered">
      <div className="modal-content">
        <div className="modal-body p-0">
          <div className="success-wrap text-center">
            <form >
              <div className="icon-success bg-danger-transparent text-danger mb-2">
                <i className="ti ti-trash" />
              </div>
              <h3 className="mb-2">Are you Sure!</h3>
              <p className="fs-16 mb-3">
                The current order will be deleted as no payment has been made so
                far.
              </p>
              <div className="d-flex align-items-center justify-content-center gap-2 flex-wrap">
                <button
                  type="button"
                  className="btn btn-md btn-secondary"
                  data-bs-dismiss="modal"
                >
                  No, Cancel
                </button>
                <button type="button" data-bs-dismiss="modal" className="btn btn-md btn-primary">
                  Yes, Delete
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
  {/* /Delete Product */}
  {/* Reset */}
  <div
    className="modal fade modal-default"
    id="reset"
    aria-labelledby="payment-completed"
  >
    <div className="modal-dialog modal-dialog-centered">
      <div className="modal-content">
        <div className="modal-body p-0">
          <div className="success-wrap text-center">
            <form >
              <div className="icon-success bg-purple-transparent text-purple mb-2">
                <i className="ti ti-transition-top" />
              </div>
              <h3 className="mb-2">Confirm Your Action</h3>
              <p className="fs-16 mb-3">
                The current order will be cleared. But not deleted if it&apos;s
                persistent. Would you like to proceed ?
              </p>
              <div className="d-flex align-items-center justify-content-center gap-2 flex-wrap">
                <button
                  type="button"
                  className="btn btn-md btn-secondary"
                  data-bs-dismiss="modal"
                >
                  No, Cancel
                </button>
                <button
                  type="button"
                  data-bs-dismiss="modal"
                  className="btn btn-md btn-primary"
                  onClick={onReset}
                >
                  Yes, Proceed
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
  {/* /Reset */}
  {/* Recent Transactions */}
  <div
    className="modal fade pos-modal"
    id="recents"
    tabIndex={-1}
    aria-hidden="true"
  >
    <div
      className="modal-dialog modal-lg modal-dialog-centered"
      role="document"
    >
      <div className="modal-content">
        <div className="modal-header">
          <h5 className="modal-title">Recent Transactions</h5>
          <button
            type="button"
            className="close"
            data-bs-dismiss="modal"
            aria-label="Close"
          >
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <div className="modal-body">
          <div className="tabs-sets">
            <ul className="nav nav-tabs" id="myTab" role="tablist">
              <li className="nav-item" role="presentation">
                <button
                  className="nav-link active"
                  id="purchase-tab"
                  data-bs-toggle="tab"
                  data-bs-target="#purchase"
                  type="button"
                  aria-controls="purchase"
                  aria-selected="true"
                  role="tab"
                >
                  Purchase
                </button>
              </li>
              <li className="nav-item" role="presentation">
                <button
                  className="nav-link"
                  id="payment-tab"
                  data-bs-toggle="tab"
                  data-bs-target="#payment"
                  type="button"
                  aria-controls="payment"
                  aria-selected="false"
                  role="tab"
                >
                  Payment
                </button>
              </li>
              <li className="nav-item" role="presentation">
                <button
                  className="nav-link"
                  id="return-tab"
                  data-bs-toggle="tab"
                  data-bs-target="#return"
                  type="button"
                  aria-controls="return"
                  aria-selected="false"
                  role="tab"
                >
                  Return
                </button>
              </li>
            </ul>
            <div className="tab-content">
              <div
                className="tab-pane fade show active"
                id="purchase"
                role="tabpanel"
                aria-labelledby="purchase-tab"
              >
                <div className="card table-list-card mb-0">
                  <div className="card-header d-flex align-items-center justify-content-between flex-wrap row-gap-3">

                    <div className="search-set">
                        <div className="search-input">
                            <Link to="#" className="btn btn-searchset">
                            <i className="ti ti-search fs-14 feather-search" />
                            </Link>
                            <div id="DataTables_Table_0_filter" className="dataTables_filter">
                            <label>
                                {" "}
                                <input
                                type="search"
                                className="form-control form-control-sm"
                                placeholder="Search"
                                aria-controls="DataTables_Table_0"
                                />
                            </label>
                            </div>
                        </div>
                    </div>
                    <ul className="table-top-head">
                     <TooltipIcons/>
                      <li>
                      <Tooltip title='Print'>
                        <Link
                          data-bs-toggle="tooltip"
                          data-bs-placement="top"
                          title="Print"
                        >
                          <i className="ti ti-printer" />
                        </Link>
                        </Tooltip>
                      </li>
                    </ul>
                  </div>
                  <div className="card-body">
                    <div className="custom-datatable-filter table-responsive">
                      <table className="table datatable">
                        <thead>
                          <tr>
                            <th className="no-sort">
                              <label className="checkboxs">
                                <input type="checkbox" className="select-all" />
                                <span className="checkmarks" />
                              </label>
                            </th>
                            <th>Customer</th>
                            <th>Reference</th>
                            <th>Date</th>
                            <th>Amount </th>
                            <th className="no-sort">Action</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td>
                              <label className="checkboxs">
                                <input type="checkbox" />
                                <span className="checkmarks" />
                              </label>
                            </td>
                            <td>
                              <div className="d-flex align-items-center">
                                <Link
                                  to="#"
                                  className="avatar avatar-md me-2"
                                >
                                  <ImageWithBasePath
                                    src="assets/img/users/user-27.jpg"
                                    alt="product"
                                  />
                                </Link>
                                <Link to="#">Carl Evans</Link>
                              </div>
                            </td>
                            <td>INV/SL0101</td>
                            <td>24 Dec 2024</td>
                            <td>$1000</td>
                            <td className="action-table-data">
                              <div className="edit-delete-action">
                                <Link
                                  className="me-2 edit-icon p-2"
                                  to="#"
                                >

                                  <Eye className="feather-eye"/>
                                </Link>
                                <Link
                                  className="me-2 p-2"
                                  to="#"
                                >

                                  <Edit className="feather-edit"/>
                                </Link>
                                <Link className="p-2" to="#">

                                <Trash2 className="feather-trash-2"/>
                                </Link>
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label className="checkboxs">
                                <input type="checkbox" />
                                <span className="checkmarks" />
                              </label>
                            </td>
                            <td>
                              <div className="d-flex align-items-center">
                                <Link
                                  to="#"
                                  className="avatar avatar-md me-2"
                                >
                                  <ImageWithBasePath
                                    src="assets/img/users/user-02.jpg"
                                    alt="product"
                                  />
                                </Link>
                                <Link to="#">
                                  Minerva Rameriz
                                </Link>
                              </div>
                            </td>
                            <td>INV/SL0102</td>
                            <td>10 Dec 2024</td>
                            <td>$1500</td>
                            <td className="action-table-data">
                              <div className="edit-delete-action">
                                <Link
                                  className="me-2 edit-icon p-2"
                                  to="#"
                                >
                                  <Eye className="feather-eye"/>
                                </Link>
                                <Link
                                  className="me-2 p-2"
                                  to="#"
                                >
                                  <Edit className="feather-edit"/>
                                </Link>
                                <Link className="p-2" to="#">
                                 <Trash2 className="feather-trash-2"/>
                                </Link>
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label className="checkboxs">
                                <input type="checkbox" />
                                <span className="checkmarks" />
                              </label>
                            </td>
                            <td>
                              <div className="d-flex align-items-center">
                                <Link
                                  to="#"
                                  className="avatar avatar-md me-2"
                                >
                                  <ImageWithBasePath
                                    src="assets/img/users/user-05.jpg"
                                    alt="product"
                                  />
                                </Link>
                                <Link to="#">Robert Lamon</Link>
                              </div>
                            </td>
                            <td>INV/SL0103</td>
                            <td>27 Nov 2024</td>
                            <td>$1500</td>
                            <td className="action-table-data">
                              <div className="edit-delete-action">
                                <Link
                                  className="me-2 edit-icon p-2"
                                  to="#"
                                >
                                  <Eye className="feather-eye"/>
                                </Link>
                                <Link
                                  className="me-2 p-2"
                                  to="#"
                                >
                                  <Edit className="feather-edit"/>
                                </Link>
                                <Link className="p-2" to="#">
                                 <Trash2 className="feather-trash-2"/>
                                </Link>
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label className="checkboxs">
                                <input type="checkbox" />
                                <span className="checkmarks" />
                              </label>
                            </td>
                            <td>
                              <div className="d-flex align-items-center">
                                <Link
                                  to="#"
                                  className="avatar avatar-md me-2"
                                >
                                  <ImageWithBasePath
                                    src="assets/img/users/user-22.jpg"
                                    alt="product"
                                  />
                                </Link>
                                <Link to="#">Patricia Lewis</Link>
                              </div>
                            </td>
                            <td>INV/SL0104</td>
                            <td>18 Nov 2024</td>
                            <td>$2000</td>
                            <td className="action-table-data">
                              <div className="edit-delete-action">
                                <Link
                                  className="me-2 edit-icon p-2"
                                  to="#"
                                >
                                  <Eye className="feather-eye"/>
                                </Link>
                                <Link
                                  className="me-2 p-2"
                                  to="#"
                                >
                                  <Edit className="feather-edit"/>
                                </Link>
                                <Link className="p-2" to="#">
                                 <Trash2 className="feather-trash-2"/>
                                </Link>
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label className="checkboxs">
                                <input type="checkbox" />
                                <span className="checkmarks" />
                              </label>
                            </td>
                            <td>
                              <div className="d-flex align-items-center">
                                <Link
                                  to="#"
                                  className="avatar avatar-md me-2"
                                >
                                  <ImageWithBasePath
                                    src="assets/img/users/user-03.jpg"
                                    alt="product"
                                  />
                                </Link>
                                <Link to="#">Mark Joslyn</Link>
                              </div>
                            </td>
                            <td>INV/SL0105</td>
                            <td>06 Nov 2024</td>
                            <td>$800</td>
                            <td className="action-table-data">
                              <div className="edit-delete-action">
                                <Link
                                  className="me-2 edit-icon p-2"
                                  to="#"
                                >
                                  <Eye className="feather-eye"/>
                                </Link>
                                <Link
                                  className="me-2 p-2"
                                  to="#"
                                >
                                  <Edit className="feather-edit"/>
                                </Link>
                                <Link className="p-2" to="#">
                                 <Trash2 className="feather-trash-2"/>
                                </Link>
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label className="checkboxs">
                                <input type="checkbox" />
                                <span className="checkmarks" />
                              </label>
                            </td>
                            <td>
                              <div className="d-flex align-items-center">
                                <Link
                                  to="#"
                                  className="avatar avatar-md me-2"
                                >
                                  <ImageWithBasePath
                                    src="assets/img/users/user-12.jpg"
                                    alt="product"
                                  />
                                </Link>
                                <Link to="#">Marsha Betts</Link>
                              </div>
                            </td>
                            <td>INV/SL0106</td>
                            <td>25 Oct 2024</td>
                            <td>$750</td>
                            <td className="action-table-data">
                              <div className="edit-delete-action">
                                <Link
                                  className="me-2 edit-icon p-2"
                                  to="#"
                                >
                                  <Eye className="feather-eye"/>
                                </Link>
                                <Link
                                  className="me-2 p-2"
                                  to="#"
                                >
                                  <Edit className="feather-edit"/>
                                </Link>
                                <Link className="p-2" to="#">
                                 <Trash2 className="feather-trash-2"/>
                                </Link>
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label className="checkboxs">
                                <input type="checkbox" />
                                <span className="checkmarks" />
                              </label>
                            </td>
                            <td>
                              <div className="d-flex align-items-center">
                                <Link
                                  to="#"
                                  className="avatar avatar-md me-2"
                                >
                                  <ImageWithBasePath
                                    src="assets/img/users/user-06.jpg"
                                    alt="product"
                                  />
                                </Link>
                                <Link to="#">Daniel Jude</Link>
                              </div>
                            </td>
                            <td>INV/SL0107</td>
                            <td>14 Oct 2024</td>
                            <td>$1300</td>
                            <td className="action-table-data">
                              <div className="edit-delete-action">
                                <Link
                                  className="me-2 edit-icon p-2"
                                  to="#"
                                >
                                  <Eye className="feather-eye"/>
                                </Link>
                                <Link
                                  className="me-2 p-2"
                                  to="#"
                                >
                                  <Edit className="feather-edit"/>
                                </Link>
                                <Link className="p-2" to="#">
                                 <Trash2 className="feather-trash-2"/>
                                </Link>
                              </div>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              <div className="tab-pane fade" id="payment" role="tabpanel">
                <div className="card table-list-card mb-0">
                  <div className="card-header d-flex align-items-center justify-content-between flex-wrap row-gap-3">
                  <div className="search-set">
                        <div className="search-input">
                            <Link to="#" className="btn btn-searchset">
                            <i className="ti ti-search fs-14 feather-search" />
                            </Link>
                            <div id="DataTables_Table_0_filter" className="dataTables_filter">
                            <label>
                                {" "}
                                <input
                                type="search"
                                className="form-control form-control-sm"
                                placeholder="Search"
                                aria-controls="DataTables_Table_0"
                                />
                            </label>
                            </div>
                        </div>
                    </div>
                    <ul className="table-top-head">
                     <TooltipIcons/>
                      <li>
                      <Tooltip title='Print'>
                        <Link
                          data-bs-toggle="tooltip"
                          data-bs-placement="top"
                          title="Print"
                        >
                          <i className="ti ti-printer" />
                        </Link>
                        </Tooltip>
                      </li>
                    </ul>
                  </div>
                  <div className="card-body">
                    <div className="custom-datatable-filter table-responsive">
                      <table className="table datatable">
                        <thead>
                          <tr>
                            <th className="no-sort">
                              <label className="checkboxs">
                                <input type="checkbox" className="select-all" />
                                <span className="checkmarks" />
                              </label>
                            </th>
                            <th>Customer</th>
                            <th>Reference</th>
                            <th>Date</th>
                            <th>Amount </th>
                            <th className="no-sort">Action</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td>
                              <label className="checkboxs">
                                <input type="checkbox" />
                                <span className="checkmarks" />
                              </label>
                            </td>
                            <td>
                              <div className="d-flex align-items-center">
                                <Link
                                  to="#"
                                  className="avatar avatar-md me-2"
                                >
                                  <ImageWithBasePath
                                    src="assets/img/users/user-27.jpg"
                                    alt="product"
                                  />
                                </Link>
                                <Link to="#">Carl Evans</Link>
                              </div>
                            </td>
                            <td>INV/SL0101</td>
                            <td>24 Dec 2024</td>
                            <td>$1000</td>
                            <td className="action-table-data">
                              <div className="edit-delete-action">
                                <Link
                                  className="me-2 edit-icon p-2"
                                  to="#"
                                >
                                  <Eye className="feather-eye"/>
                                </Link>
                                <Link
                                  className="me-2 p-2"
                                  to="#"
                                >
                                  <Edit className="feather-edit"/>
                                </Link>
                                <Link className="p-2" to="#">
                                 <Trash2 className="feather-trash-2"/>
                                </Link>
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label className="checkboxs">
                                <input type="checkbox" />
                                <span className="checkmarks" />
                              </label>
                            </td>
                            <td>
                              <div className="d-flex align-items-center">
                                <Link
                                  to="#"
                                  className="avatar avatar-md me-2"
                                >
                                  <ImageWithBasePath
                                    src="assets/img/users/user-02.jpg"
                                    alt="product"
                                  />
                                </Link>
                                <Link to="#">
                                  Minerva Rameriz
                                </Link>
                              </div>
                            </td>
                            <td>INV/SL0102</td>
                            <td>10 Dec 2024</td>
                            <td>$1500</td>
                            <td className="action-table-data">
                              <div className="edit-delete-action">
                                <Link
                                  className="me-2 edit-icon p-2"
                                  to="#"
                                >
                                  <Eye className="feather-eye"/>
                                </Link>
                                <Link
                                  className="me-2 p-2"
                                  to="#"
                                >
                                  <Edit className="feather-edit"/>
                                </Link>
                                <Link className="p-2" to="#">
                                 <Trash2 className="feather-trash-2"/>
                                </Link>
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label className="checkboxs">
                                <input type="checkbox" />
                                <span className="checkmarks" />
                              </label>
                            </td>
                            <td>
                              <div className="d-flex align-items-center">
                                <Link
                                  to="#"
                                  className="avatar avatar-md me-2"
                                >
                                  <ImageWithBasePath
                                    src="assets/img/users/user-05.jpg"
                                    alt="product"
                                  />
                                </Link>
                                <Link to="#">Robert Lamon</Link>
                              </div>
                            </td>
                            <td>INV/SL0103</td>
                            <td>27 Nov 2024</td>
                            <td>$1500</td>
                            <td className="action-table-data">
                              <div className="edit-delete-action">
                                <Link
                                  className="me-2 edit-icon p-2"
                                  to="#"
                                >
                                  <Eye className="feather-eye"/>
                                </Link>
                                <Link
                                  className="me-2 p-2"
                                  to="#"
                                >
                                  <Edit className="feather-edit"/>
                                </Link>
                                <Link className="p-2" to="#">
                                 <Trash2 className="feather-trash-2"/>
                                </Link>
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label className="checkboxs">
                                <input type="checkbox" />
                                <span className="checkmarks" />
                              </label>
                            </td>
                            <td>
                              <div className="d-flex align-items-center">
                                <Link
                                  to="#"
                                  className="avatar avatar-md me-2"
                                >
                                  <ImageWithBasePath
                                    src="assets/img/users/user-22.jpg"
                                    alt="product"
                                  />
                                </Link>
                                <Link to="#">Patricia Lewis</Link>
                              </div>
                            </td>
                            <td>INV/SL0104</td>
                            <td>18 Nov 2024</td>
                            <td>$2000</td>
                            <td className="action-table-data">
                              <div className="edit-delete-action">
                                <Link
                                  className="me-2 edit-icon p-2"
                                  to="#"
                                >
                                  <Eye className="feather-eye"/>
                                </Link>
                                <Link
                                  className="me-2 p-2"
                                  to="#"
                                >
                                  <Edit className="feather-edit"/>
                                </Link>
                                <Link className="p-2" to="#">
                                 <Trash2 className="feather-trash-2"/>
                                </Link>
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label className="checkboxs">
                                <input type="checkbox" />
                                <span className="checkmarks" />
                              </label>
                            </td>
                            <td>
                              <div className="d-flex align-items-center">
                                <Link
                                  to="#"
                                  className="avatar avatar-md me-2"
                                >
                                  <ImageWithBasePath
                                    src="assets/img/users/user-03.jpg"
                                    alt="product"
                                  />
                                </Link>
                                <Link to="#">Mark Joslyn</Link>
                              </div>
                            </td>
                            <td>INV/SL0105</td>
                            <td>06 Nov 2024</td>
                            <td>$800</td>
                            <td className="action-table-data">
                              <div className="edit-delete-action">
                                <Link
                                  className="me-2 edit-icon p-2"
                                  to="#"
                                >
                                  <Eye className="feather-eye"/>
                                </Link>
                                <Link
                                  className="me-2 p-2"
                                  to="#"
                                >
                                  <Edit className="feather-edit"/>
                                </Link>
                                <Link className="p-2" to="#">
                                 <Trash2 className="feather-trash-2"/>
                                </Link>
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label className="checkboxs">
                                <input type="checkbox" />
                                <span className="checkmarks" />
                              </label>
                            </td>
                            <td>
                              <div className="d-flex align-items-center">
                                <Link
                                  to="#"
                                  className="avatar avatar-md me-2"
                                >
                                  <ImageWithBasePath
                                    src="assets/img/users/user-12.jpg"
                                    alt="product"
                                  />
                                </Link>
                                <Link to="#">Marsha Betts</Link>
                              </div>
                            </td>
                            <td>INV/SL0106</td>
                            <td>25 Oct 2024</td>
                            <td>$750</td>
                            <td className="action-table-data">
                              <div className="edit-delete-action">
                                <Link
                                  className="me-2 edit-icon p-2"
                                  to="#"
                                >
                                  <Eye className="feather-eye"/>
                                </Link>
                                <Link
                                  className="me-2 p-2"
                                  to="#"
                                >
                                  <Edit className="feather-edit"/>
                                </Link>
                                <Link className="p-2" to="#">
                                 <Trash2 className="feather-trash-2"/>
                                </Link>
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label className="checkboxs">
                                <input type="checkbox" />
                                <span className="checkmarks" />
                              </label>
                            </td>
                            <td>
                              <div className="d-flex align-items-center">
                                <Link
                                  to="#"
                                  className="avatar avatar-md me-2"
                                >
                                  <ImageWithBasePath
                                    src="assets/img/users/user-06.jpg"
                                    alt="product"
                                  />
                                </Link>
                                <Link to="#">Daniel Jude</Link>
                              </div>
                            </td>
                            <td>INV/SL0107</td>
                            <td>14 Oct 2024</td>
                            <td>$1300</td>
                            <td className="action-table-data">
                              <div className="edit-delete-action">
                                <Link
                                  className="me-2 edit-icon p-2"
                                  to="#"
                                >
                                  <Eye className="feather-eye"/>
                                </Link>
                                <Link
                                  className="me-2 p-2"
                                  to="#"
                                >
                                  <Edit className="feather-edit"/>
                                </Link>
                                <Link className="p-2" to="#">
                                 <Trash2 className="feather-trash-2"/>
                                </Link>
                              </div>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              <div className="tab-pane fade" id="return" role="tabpanel">
                <div className="card table-list-card mb-0">
                  <div className="card-header d-flex align-items-center justify-content-between flex-wrap row-gap-3">
                  <div className="search-set">
                        <div className="search-input">
                            <Link to="#" className="btn btn-searchset">
                            <i className="ti ti-search fs-14 feather-search" />
                            </Link>
                            <div id="DataTables_Table_0_filter" className="dataTables_filter">
                            <label>
                                {" "}
                                <input
                                type="search"
                                className="form-control form-control-sm"
                                placeholder="Search"
                                aria-controls="DataTables_Table_0"
                                />
                            </label>
                            </div>
                        </div>
                    </div>
                    <ul className="table-top-head">
                     <TooltipIcons/>
                      <li>
                      <Tooltip title='Print'>
                        <Link
                          data-bs-toggle="tooltip"
                          data-bs-placement="top"
                          title="Print"
                        >
                          <i className="ti ti-printer" />
                        </Link>
                        </Tooltip>
                      </li>
                    </ul>
                  </div>
                  <div className="card-body">
                    <div className="custom-datatable-filter table-responsive">
                      <table className="table datatable">
                        <thead>
                          <tr>
                            <th className="no-sort">
                              <label className="checkboxs">
                                <input type="checkbox" className="select-all" />
                                <span className="checkmarks" />
                              </label>
                            </th>
                            <th>Customer</th>
                            <th>Reference</th>
                            <th>Date</th>
                            <th>Amount </th>
                            <th className="no-sort">Action</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td>
                              <label className="checkboxs">
                                <input type="checkbox" />
                                <span className="checkmarks" />
                              </label>
                            </td>
                            <td>
                              <div className="d-flex align-items-center">
                                <Link
                                  to="#"
                                  className="avatar avatar-md me-2"
                                >
                                  <ImageWithBasePath
                                    src="assets/img/users/user-27.jpg"
                                    alt="product"
                                  />
                                </Link>
                                <Link to="#">Carl Evans</Link>
                              </div>
                            </td>
                            <td>INV/SL0101</td>
                            <td>24 Dec 2024</td>
                            <td>$1000</td>
                            <td className="action-table-data">
                              <div className="edit-delete-action">
                                <Link
                                  className="me-2 edit-icon p-2"
                                  to="#"
                                >
                                  <Eye className="feather-eye"/>
                                </Link>
                                <Link
                                  className="me-2 p-2"
                                  to="#"
                                >
                                  <Edit className="feather-edit"/>
                                </Link>
                                <Link className="p-2" to="#">
                                 <Trash2 className="feather-trash-2"/>
                                </Link>
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label className="checkboxs">
                                <input type="checkbox" />
                                <span className="checkmarks" />
                              </label>
                            </td>
                            <td>
                              <div className="d-flex align-items-center">
                                <Link
                                  to="#"
                                  className="avatar avatar-md me-2"
                                >
                                  <ImageWithBasePath
                                    src="assets/img/users/user-02.jpg"
                                    alt="product"
                                  />
                                </Link>
                                <Link to="#">
                                  Minerva Rameriz
                                </Link>
                              </div>
                            </td>
                            <td>INV/SL0102</td>
                            <td>10 Dec 2024</td>
                            <td>$1500</td>
                            <td className="action-table-data">
                              <div className="edit-delete-action">
                                <Link
                                  className="me-2 edit-icon p-2"
                                  to="#"
                                >
                                  <Eye className="feather-eye"/>
                                </Link>
                                <Link
                                  className="me-2 p-2"
                                  to="#"
                                >
                                  <Edit className="feather-edit"/>
                                </Link>
                                <Link className="p-2" to="#">
                                 <Trash2 className="feather-trash-2"/>
                                </Link>
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label className="checkboxs">
                                <input type="checkbox" />
                                <span className="checkmarks" />
                              </label>
                            </td>
                            <td>
                              <div className="d-flex align-items-center">
                                <Link
                                  to="#"
                                  className="avatar avatar-md me-2"
                                >
                                  <ImageWithBasePath
                                    src="assets/img/users/user-05.jpg"
                                    alt="product"
                                  />
                                </Link>
                                <Link to="#">Robert Lamon</Link>
                              </div>
                            </td>
                            <td>INV/SL0103</td>
                            <td>27 Nov 2024</td>
                            <td>$1500</td>
                            <td className="action-table-data">
                              <div className="edit-delete-action">
                                <Link
                                  className="me-2 edit-icon p-2"
                                  to="#"
                                >
                                  <Eye className="feather-eye"/>
                                </Link>
                                <Link
                                  className="me-2 p-2"
                                  to="#"
                                >
                                  <Edit className="feather-edit"/>
                                </Link>
                                <Link className="p-2" to="#">
                                 <Trash2 className="feather-trash-2"/>
                                </Link>
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label className="checkboxs">
                                <input type="checkbox" />
                                <span className="checkmarks" />
                              </label>
                            </td>
                            <td>
                              <div className="d-flex align-items-center">
                                <Link
                                  to="#"
                                  className="avatar avatar-md me-2"
                                >
                                  <ImageWithBasePath
                                    src="assets/img/users/user-22.jpg"
                                    alt="product"
                                  />
                                </Link>
                                <Link to="#">Patricia Lewis</Link>
                              </div>
                            </td>
                            <td>INV/SL0104</td>
                            <td>18 Nov 2024</td>
                            <td>$2000</td>
                            <td className="action-table-data">
                              <div className="edit-delete-action">
                                <Link
                                  className="me-2 edit-icon p-2"
                                  to="#"
                                >
                                  <Eye className="feather-eye"/>
                                </Link>
                                <Link
                                  className="me-2 p-2"
                                  to="#"
                                >
                                  <Edit className="feather-edit"/>
                                </Link>
                                <Link className="p-2" to="#">
                                 <Trash2 className="feather-trash-2"/>
                                </Link>
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label className="checkboxs">
                                <input type="checkbox" />
                                <span className="checkmarks" />
                              </label>
                            </td>
                            <td>
                              <div className="d-flex align-items-center">
                                <Link
                                  to="#"
                                  className="avatar avatar-md me-2"
                                >
                                  <ImageWithBasePath
                                    src="assets/img/users/user-03.jpg"
                                    alt="product"
                                  />
                                </Link>
                                <Link to="#">Mark Joslyn</Link>
                              </div>
                            </td>
                            <td>INV/SL0105</td>
                            <td>06 Nov 2024</td>
                            <td>$800</td>
                            <td className="action-table-data">
                              <div className="edit-delete-action">
                                <Link
                                  className="me-2 edit-icon p-2"
                                  to="#"
                                >
                                  <Eye className="feather-eye"/>
                                </Link>
                                <Link
                                  className="me-2 p-2"
                                  to="#"
                                >
                                  <Edit className="feather-edit"/>
                                </Link>
                                <Link className="p-2" to="#">
                                 <Trash2 className="feather-trash-2"/>
                                </Link>
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label className="checkboxs">
                                <input type="checkbox" />
                                <span className="checkmarks" />
                              </label>
                            </td>
                            <td>
                              <div className="d-flex align-items-center">
                                <Link
                                  to="#"
                                  className="avatar avatar-md me-2"
                                >
                                  <ImageWithBasePath
                                    src="assets/img/users/user-12.jpg"
                                    alt="product"
                                  />
                                </Link>
                                <Link to="#">Marsha Betts</Link>
                              </div>
                            </td>
                            <td>INV/SL0106</td>
                            <td>25 Oct 2024</td>
                            <td>$750</td>
                            <td className="action-table-data">
                              <div className="edit-delete-action">
                                <Link
                                  className="me-2 edit-icon p-2"
                                  to="#"
                                >
                                  <Eye className="feather-eye"/>
                                </Link>
                                <Link
                                  className="me-2 p-2"
                                  to="#"
                                >
                                  <Edit className="feather-edit"/>
                                </Link>
                                <Link className="p-2" to="#">
                                 <Trash2 className="feather-trash-2"/>
                                </Link>
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td>
                              <label className="checkboxs">
                                <input type="checkbox" />
                                <span className="checkmarks" />
                              </label>
                            </td>
                            <td>
                              <div className="d-flex align-items-center">
                                <Link
                                  to="#"
                                  className="avatar avatar-md me-2"
                                >
                                  <ImageWithBasePath
                                    src="assets/img/users/user-06.jpg"
                                    alt="product"
                                  />
                                </Link>
                                <Link to="#">Daniel Jude</Link>
                              </div>
                            </td>
                            <td>INV/SL0107</td>
                            <td>14 Oct 2024</td>
                            <td>$1300</td>
                            <td className="action-table-data">
                              <div className="edit-delete-action">
                                <Link
                                  className="me-2 edit-icon p-2"
                                  to="#"
                                >
                                  <Eye className="feather-eye"/>
                                </Link>
                                <Link
                                  className="me-2 p-2"
                                  to="#"
                                >
                                  <Edit className="feather-edit"/>
                                </Link>
                                <Link className="p-2" to="#">
                                 <Trash2 className="feather-trash-2"/>
                                </Link>
                              </div>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  {/* /Recent Transactions */}
  {/* Orders */}
  <div
    className="modal fade pos-modal"
    id="orders"
    tabIndex={-1}
    aria-hidden="true"
  >
    <div
      className="modal-dialog modal-md modal-dialog-centered"
      role="document"
    >
      <div className="modal-content">
        <div className="modal-header">
          <h5 className="modal-title">Orders</h5>
          <button
            type="button"
            className="close"
            data-bs-dismiss="modal"
            aria-label="Close"
          >
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <div className="modal-body">
          <div className="tabs-sets">
            <ul className="nav nav-tabs" id="myTabs" role="tablist">
              <li className="nav-item" role="presentation">
                <button
                  className="nav-link active"
                  id="onhold-tab"
                  data-bs-toggle="tab"
                  data-bs-target="#onhold"
                  type="button"
                  aria-controls="onhold"
                  aria-selected="true"
                  role="tab"
                >
                  Onhold
                </button>
              </li>
              <li className="nav-item" role="presentation">
                <button
                  className="nav-link"
                  id="unpaid-tab"
                  data-bs-toggle="tab"
                  data-bs-target="#unpaid"
                  type="button"
                  aria-controls="unpaid"
                  aria-selected="false"
                  role="tab"
                >
                  Unpaid
                </button>
              </li>
              <li className="nav-item" role="presentation">
                <button
                  className="nav-link"
                  id="paid-tab"
                  data-bs-toggle="tab"
                  data-bs-target="#paid"
                  type="button"
                  aria-controls="paid"
                  aria-selected="false"
                  role="tab"
                >
                  Paid
                </button>
              </li>
            </ul>
            <div className="tab-content">
              <div
                className="tab-pane fade show active"
                id="onhold"
                role="tabpanel"
                aria-labelledby="onhold-tab"
              >
                <div className="input-icon-start pos-search position-relative mb-3">
                  <span className="input-icon-addon">
                    <i className="ti ti-search" />
                  </span>
                  <input
                    type="text"
                    className="form-control"
                    placeholder="Search Product"
                  />
                </div>
                <div className="order-body">
                  <div className="card bg-light mb-3">
                    <div className="card-body">
                      <span className="badge bg-dark fs-12 mb-2">
                        Order ID : #45698
                      </span>
                      <div className="row g-3">
                        <div className="col-md-6">
                          <p className="fs-15 mb-1">
                            <span className="fs-14 fw-bold text-gray-9">
                              Cashier :
                            </span>{" "}
                            admin
                          </p>
                          <p className="fs-15">
                            <span className="fs-14 fw-bold text-gray-9">
                              Total :
                            </span>{" "}
                            $900
                          </p>
                        </div>
                        <div className="col-md-6">
                          <p className="fs-15 mb-1">
                            <span className="fs-14 fw-bold text-gray-9">
                              Customer :
                            </span>{" "}
                            Botsford
                          </p>
                          <p className="fs-15">
                            <span className="fs-14 fw-bold text-gray-9">
                              Date :
                            </span>{" "}
                            24 Dec 2024 13:39:11
                          </p>
                        </div>
                      </div>
                      <div className="bg-info-transparent p-1 rounded text-center my-3">
                        <p className="text-info fw-medium">
                          Customer need to recheck the product once
                        </p>
                      </div>
                      <div className="d-flex align-items-center justify-content-center flex-wrap gap-2">
                        <Link
                          to="#"
                          className="btn btn-md btn-orange"
                        >
                          Open Order
                        </Link>
                        <Link
                          to="#"
                          className="btn btn-md btn-teal"
                          data-bs-dismiss="modal"
                          data-bs-toggle="modal"
                          data-bs-target="#products"
                        >
                          View Products
                        </Link>
                        <Link
                          to="#"
                          className="btn btn-md btn-indigo"
                        >
                          Print
                        </Link>
                      </div>
                    </div>
                  </div>
                  <div className="card bg-light mb-0">
                    <div className="card-body">
                      <span className="badge bg-dark fs-12 mb-2">
                        Order ID : #666659
                      </span>
                      <div className="mb-3">
                        <div className="row g-3">
                          <div className="col-md-6">
                            <p className="fs-15 mb-1">
                              <span className="fs-14 fw-bold text-gray-9">
                                Cashier :
                              </span>{" "}
                              admin
                            </p>
                            <p className="fs-15">
                              <span className="fs-14 fw-bold text-gray-9">
                                Total :
                              </span>{" "}
                              $900
                            </p>
                          </div>
                          <div className="col-md-6">
                            <p className="fs-15 mb-1">
                              <span className="fs-14 fw-bold text-gray-9">
                                Customer :
                              </span>{" "}
                              Botsford
                            </p>
                            <p className="fs-15">
                              <span className="fs-14 fw-bold text-gray-9">
                                Date :
                              </span>{" "}
                              24 Dec 2024 13:39:11
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="tab-pane fade" id="unpaid" role="tabpanel">
                <div className="input-icon-start pos-search position-relative mb-3">
                  <span className="input-icon-addon">
                    <i className="ti ti-search" />
                  </span>
                  <input
                    type="text"
                    className="form-control"
                    placeholder="Search Product"
                  />
                </div>
                <div className="order-body">
                  <div className="card bg-light mb-3">
                    <div className="card-body">
                      <span className="badge bg-dark fs-12 mb-2">
                        Order ID : #45698
                      </span>
                      <div className="row g-3">
                        <div className="col-md-6">
                          <p className="fs-15 mb-1">
                            <span className="fs-14 fw-bold text-gray-9">
                              Cashier :
                            </span>{" "}
                            admin
                          </p>
                          <p className="fs-15">
                            <span className="fs-14 fw-bold text-gray-9">
                              Total :
                            </span>{" "}
                            $900
                          </p>
                        </div>
                        <div className="col-md-6">
                          <p className="fs-15 mb-1">
                            <span className="fs-14 fw-bold text-gray-9">
                              Customer :
                            </span>{" "}
                            Anastasia
                          </p>
                          <p className="fs-15">
                            <span className="fs-14 fw-bold text-gray-9">
                              Date :
                            </span>{" "}
                            24 Dec 2024 13:39:11
                          </p>
                        </div>
                      </div>
                      <div className="bg-info-transparent p-1 rounded text-center my-3">
                        <p className="text-info fw-medium">
                          Customer need to recheck the product once
                        </p>
                      </div>
                      <div className="d-flex align-items-center justify-content-center flex-wrap gap-2">
                        <Link
                          to="#"
                          className="btn btn-md btn-orange"
                        >
                          Open Order
                        </Link>
                        <Link
                          to="#"
                          className="btn btn-md btn-teal"
                          data-bs-dismiss="modal"
                          data-bs-toggle="modal"
                          data-bs-target="#products"
                        >
                          View Products
                        </Link>
                        <Link
                          to="#"
                          className="btn btn-md btn-indigo"
                        >
                          Print
                        </Link>
                      </div>
                    </div>
                  </div>
                  <div className="card bg-light mb-0">
                    <div className="card-body">
                      <span className="badge bg-dark fs-12 mb-2">
                        Order ID : #666659
                      </span>
                      <div className="row g-3">
                        <div className="col-md-6">
                          <p className="fs-15 mb-1">
                            <span className="fs-14 fw-bold text-gray-9">
                              Cashier :
                            </span>{" "}
                            admin
                          </p>
                          <p className="fs-15">
                            <span className="fs-14 fw-bold text-gray-9">
                              Total :
                            </span>{" "}
                            $900
                          </p>
                        </div>
                        <div className="col-md-6">
                          <p className="fs-15 mb-1">
                            <span className="fs-14 fw-bold text-gray-9">
                              Customer :
                            </span>{" "}
                            Lucia
                          </p>
                          <p className="fs-15">
                            <span className="fs-14 fw-bold text-gray-9">
                              Date :
                            </span>{" "}
                            24 Dec 2024 13:39:11
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="tab-pane fade" id="paid" role="tabpanel">
                <div className="input-icon-start pos-search position-relative mb-3">
                  <span className="input-icon-addon">
                    <i className="ti ti-search" />
                  </span>
                  <input
                    type="text"
                    className="form-control"
                    placeholder="Search Product"
                  />
                </div>
                <div className="order-body">
                  <div className="card bg-light mb-3">
                    <div className="card-body">
                      <span className="badge bg-dark fs-12 mb-2">
                        Order ID : #45698
                      </span>
                      <div className="row g-3">
                        <div className="col-md-6">
                          <p className="fs-15 mb-1">
                            <span className="fs-14 fw-bold text-gray-9">
                              Cashier :
                            </span>{" "}
                            admin
                          </p>
                          <p className="fs-15">
                            <span className="fs-14 fw-bold text-gray-9">
                              Total :
                            </span>{" "}
                            $1000
                          </p>
                        </div>
                        <div className="col-md-6">
                          <p className="fs-15 mb-1">
                            <span className="fs-14 fw-bold text-gray-9">
                              Customer :
                            </span>{" "}
                            Hugo
                          </p>
                          <p className="fs-15">
                            <span className="fs-14 fw-bold text-gray-9">
                              Date :
                            </span>{" "}
                            24 Dec 2024 13:39:11
                          </p>
                        </div>
                      </div>
                      <div className="bg-info-transparent p-1 rounded text-center my-3">
                        <p className="text-info fw-medium">
                          Customer need to recheck the product once
                        </p>
                      </div>
                      <div className="d-flex align-items-center justify-content-center flex-wrap gap-2">
                        <Link
                          to="#"
                          className="btn btn-md btn-orange"
                        >
                          Open Order
                        </Link>
                        <Link
                          to="#"
                          className="btn btn-md btn-teal"
                          data-bs-dismiss="modal"
                          data-bs-toggle="modal"
                          data-bs-target="#products"
                        >
                          View Products
                        </Link>
                        <Link
                          to="#"
                          className="btn btn-md btn-indigo"
                        >
                          Print
                        </Link>
                      </div>
                    </div>
                  </div>
                  <div className="card bg-light mb-0">
                    <div className="card-body">
                      <span className="badge bg-dark fs-12 mb-2">
                        Order ID : #666659
                      </span>
                      <div className="row g-3">
                        <div className="col-md-6">
                          <p className="fs-15 mb-1">
                            <span className="fs-14 fw-bold text-gray-9">
                              Cashier :
                            </span>{" "}
                            admin
                          </p>
                          <p className="fs-15">
                            <span className="fs-14 fw-bold text-gray-9">
                              Total :
                            </span>{" "}
                            $9100
                          </p>
                        </div>
                        <div className="col-md-6">
                          <p className="fs-15 mb-1">
                            <span className="fs-14 fw-bold text-gray-9">
                              Customer :
                            </span>{" "}
                            Antonio
                          </p>
                          <p className="fs-15">
                            <span className="fs-14 fw-bold text-gray-9">
                              Date :
                            </span>{" "}
                            23 Dec 2024 13:39:11
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  {/* /Orders */}
  {/* Scan */}
  <div className="modal fade modal-default" id="scan-payment">
    <div className="modal-dialog modal-dialog-centered">
      <div className="modal-content">
        <div className="modal-body p-0">
          <button
            type="button"
            className="close"
            data-bs-dismiss="modal"
            aria-label="Close"
          >
            <span aria-hidden="true">×</span>
          </button>
          <div className="success-wrap scan-wrap text-center">
            <h5>
              <span className="text-gray-6">Amount to Pay :</span> $150
            </h5>
            <div className="scan-img">
              <ImageWithBasePath src="assets/img/icons/scan-img.svg" alt="img" />
            </div>
            <p className="mb-3">
              Scan your Phone or UPI App to Complete the payment
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
  {/* /Scan */}
  {/* Order Tax */}
  <div className="modal fade modal-default" id="order-tax">
    <div className="modal-dialog modal-dialog-centered modal-lg">
      <div className="modal-content">
        <div className="modal-header">
          <h5 className="modal-title">Tax Selection</h5>
          <button
            type="button"
            className="close"
            data-bs-dismiss="modal"
            aria-label="Close"
          >
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <form onSubmit={(e) => {
          e.preventDefault();

          // Get tax rate from hidden input
          const hiddenInput = document.getElementById('selectedTaxRate');
          const taxRate = hiddenInput ? parseFloat(hiddenInput.value) || 0 : 0;

          // Update payment summary
          const newPaymentSummary = { ...paymentSummary, taxRate: taxRate };
          setPaymentSummary(newPaymentSummary);
          savePaymentSummary(newPaymentSummary);

          // Recalculate order total
          recalculateOrderTotal(discountSettings, newPaymentSummary);

          // Close modal
          document.querySelector('#order-tax .close').click();
        }}>
          <div className="modal-body pb-1">
            {/* Company Type Information */}
            {companyTaxSettings && (
              <div className="mb-3">
                <div className={`alert ${companyTaxSettings.isServiceTaxCompany ? 'alert-info' : 'alert-light'} d-flex align-items-center py-2`}>
                  <i className={`ti ${companyTaxSettings.isServiceTaxCompany ? 'ti-briefcase' : 'ti-building'} me-2`}></i>
                  <div className="flex-grow-1">
                    <small>
                      <strong>Company Type: </strong>
                      {companyTaxSettings.isServiceTaxCompany ? 'Service Tax Company' : 'General Business Company'}
                      {companyTaxSettings.isServiceTaxCompany && (
                        <span className="text-muted ms-2">- Only service tax categories are shown</span>
                      )}
                    </small>
                  </div>
                </div>
              </div>
            )}

            {/* Tax Category Selection Section */}
            <div className="mb-4">
              <div className="d-flex justify-content-between align-items-center mb-3">
                <h6 className="mb-0">
                  <i className="ti ti-category me-2"></i>
                  Step 1: Select Tax Category
                </h6>
                <div className="d-flex gap-2">
                  <button
                    type="button"
                    className="btn btn-sm btn-outline-secondary"
                    onClick={loadTaxRatesFromAPI}
                    disabled={loadingTaxRates}
                    title="Refresh tax categories from API"
                  >
                    <i className={`ti ti-refresh ${loadingTaxRates ? 'spin' : ''}`}></i>
                  </button>

                  <button
                    type="button"
                    className="btn btn-sm btn-outline-primary"
                    data-bs-toggle="modal"
                    data-bs-target="#tax-categories-settings"
                    onClick={() => {
                      document.querySelector('#order-tax .close').click();
                      // Set up listener for when tax categories modal closes
                      const taxCategoriesModal = document.getElementById('tax-categories-settings');
                      if (taxCategoriesModal) {
                        const handleModalHidden = () => {
                          // Refresh tax rates when modal closes
                          console.log('Tax categories modal closed, refreshing tax rates...');
                          loadTaxRatesFromAPI();
                          taxCategoriesModal.removeEventListener('hidden.bs.modal', handleModalHidden);
                        };
                        taxCategoriesModal.addEventListener('hidden.bs.modal', handleModalHidden);
                      }
                    }}
                  >
                    <i className="ti ti-settings me-1"></i>
                    Manage Tax Categories
                  </button>
                </div>
              </div>

              {/* Tax Categories Grid */}
              <div className="row g-3 mb-3">
                {taxCategories.length > 0 ? (
                  taxCategories.map((taxCategory, index) => (
                    <div key={index} className="col-md-6 col-lg-4">
                      <div
                        className={`card border cursor-pointer h-100 ${
                          selectedTaxCategory?.id === taxCategory.id
                            ? 'border-primary bg-primary-light'
                            : 'border-light'
                        }`}
                        onClick={() => handleTaxCategorySelect(taxCategory)}
                        style={{ cursor: 'pointer' }}
                      >
                        <div className="card-body p-3 text-center">
                          <div className="d-flex justify-content-between align-items-start mb-2">
                            <h6 className="card-title mb-0 text-start flex-grow-1">
                              {taxCategory.displayName}
                            </h6>
                            <div className="d-flex flex-column gap-1">
                              {taxCategory.code === 'SALESTAX' && (
                                <span className="badge bg-success">Sales Tax</span>
                              )}
                              {taxCategory.code === 'SERVICETAX' && (
                                <span className="badge bg-info">Service Tax</span>
                              )}
                              {taxCategory.code === 'NOTAX' && (
                                <span className="badge bg-secondary">No Tax</span>
                              )}
                            </div>
                          </div>
                          <div className="text-primary fw-bold fs-6">
                            {taxCategory.hasRates ? (
                              `${taxCategory.taxRates.length} tax rates available`
                            ) : (
                              'No tax applicable'
                            )}
                          </div>
                          {taxCategory.description && (
                            <small className="text-muted d-block mt-1">
                              {taxCategory.description}
                            </small>
                          )}
                          {selectedTaxCategory?.id === taxCategory.id && (
                            <div className="mt-2">
                              <i className="ti ti-check-circle text-primary"></i>
                              <small className="text-primary ms-1">Selected</small>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="col-12">
                    <div className="alert alert-warning d-flex align-items-center">
                      <i className="ti ti-alert-triangle me-2"></i>
                      <div>
                        <strong>No tax categories available.</strong>
                        <br />
                        <small>Please check your API connection or try refreshing.</small>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Tax Rate Selection Section */}
            <div className="mb-3">
              <div className="d-flex justify-content-between align-items-center mb-2">
                <h6 className="mb-0">
                  <i className="ti ti-percentage me-2"></i>
                  Step 2: Confirm Tax Rate
                </h6>
                {selectedTaxCategory && (
                  <small className="text-muted">
                    Category: {selectedTaxCategory.displayName}
                  </small>
                )}
              </div>

              {selectedTaxCategory ? (
                selectedTaxCategory.hasRates ? (
                  <>
                    <Select
                      key={selectedTaxCategory?.id || 'no-category'}
                      options={availableTaxRates.map(rate => ({
                        value: rate.code, // Use tax rate code as value
                        label: rate.displayName,
                        taxRate: rate
                      }))}
                      classNamePrefix="react-select"
                      className="tax-rate-select"
                      placeholder="Select Tax Rate"
                      isSearchable={true}
                      isClearable={false}
                      value={selectedTaxRate ? {
                        value: selectedTaxRate.code,
                        label: selectedTaxRate.displayName,
                        taxRate: selectedTaxRate
                      } : null}
                      onChange={(selectedOption) => {
                        if (selectedOption) {
                          handleTaxRateSelect(selectedOption.taxRate);
                        }
                      }}
                      noOptionsMessage={() => "No tax rates available"}
                      blurInputOnSelect={true}
                      closeMenuOnSelect={true}

                      formatOptionLabel={(option) => {
                        return (
                          <div className="d-flex justify-content-between align-items-center">
                            <span>{option.taxRate.name}</span>
                            <span className="badge bg-primary ms-2">{option.taxRate.chargePercentage}%</span>
                          </div>
                        );
                      }}
                      styles={{
                        control: (provided) => ({
                          ...provided,
                          borderColor: selectedTaxRate ? '#28a745' : provided.borderColor,
                          boxShadow: selectedTaxRate ? '0 0 0 0.2rem rgba(40, 167, 69, 0.25)' : provided.boxShadow
                        }),
                        option: (provided) => {
                          // The CSS styles will handle the focus/hover/selection states
                          // We just need to return the base provided styles
                          return {
                            ...provided
                          };
                        },
                        singleValue: (provided) => ({
                          ...provided,
                          color: '#333333'
                        }),
                        menuList: (provided) => ({
                          ...provided,
                          padding: 0
                        }),
                        menu: (provided) => ({
                          ...provided,
                          border: '1px solid #dee2e6',
                          boxShadow: '0 0.125rem 0.25rem rgba(0, 0, 0, 0.075)'
                        }),
                        // Additional styling to ensure consistent behavior
                        indicatorSeparator: () => ({
                          display: 'none'
                        }),
                        dropdownIndicator: (provided) => ({
                          ...provided,
                          color: '#6c757d'
                        })
                      }}
                    />

                    {/* Selected Tax Rate Confirmation */}
                    {selectedTaxRate && (
                      <div className="mt-3 p-3 bg-light border border-success rounded" style={{backgroundColor: '#d4edda'}}>
                        <div className="d-flex align-items-center justify-content-between">
                          <div className="d-flex align-items-center">
                            <i className="ti ti-check-circle text-success me-2"></i>
                            <div>
                              <h6 className="mb-1 text-success">Selected Tax Rate</h6>
                              <p className="mb-0 small text-muted">
                                {selectedTaxRate.code} - {selectedTaxRate.name}
                              </p>
                            </div>
                          </div>
                          <div className="text-end">
                            <span className="badge bg-success fs-6 px-3 py-2">
                              {selectedTaxRate.chargePercentage}%
                            </span>
                          </div>
                        </div>
                        {selectedTaxRate.description && (
                          <small className="text-muted d-block mt-2">
                            <i className="ti ti-info-circle me-1"></i>
                            {selectedTaxRate.description}
                          </small>
                        )}
                      </div>
                    )}
                  </>
                ) : (
                  <div className="alert alert-info d-flex align-items-center">
                    <i className="ti ti-info-circle me-2"></i>
                    <div>
                      <strong>No Tax Applicable</strong>
                      <br />
                      <small>This category ({selectedTaxCategory.name}) does not require tax rates.</small>
                    </div>
                  </div>
                )
              ) : (
                <div className="alert alert-secondary d-flex align-items-center">
                  <i className="ti ti-arrow-up me-2"></i>
                  <div>
                    <strong>Please select a tax category first</strong>
                    <br />
                    <small>Choose a tax category from Step 1 above to see available tax rates.</small>
                  </div>
                </div>
              )}

              <input type="hidden" id="selectedTaxRate" name="taxRate" value={paymentSummary.taxRate} />

              {/* Tax Calculation Preview */}
              {paymentSummary.taxRate > 0 && (
                <div className="mt-3 p-3 bg-light rounded">
                  <h6 className="mb-2">
                    <i className="ti ti-calculator me-2"></i>
                    Tax Calculation Preview
                  </h6>
                  <div className="row g-2 small">
                    <div className="col-6">
                      <strong>Selected Tax Rate:</strong>
                    </div>
                    <div className="col-6 text-end">
                      {paymentSummary.taxRate}%
                    </div>
                    <div className="col-6">
                      <strong>Tax Amount:</strong>
                    </div>
                    <div className="col-6 text-end text-primary fw-bold">
                      {formatCurrency(
                        calculateOrderTotal({
                          discountType: discountSettings.discountType,
                          discountValue: discountSettings.discountValue,
                          customPercentage: discountSettings.customPercentage,
                          serviceCharges: paymentSummary.serviceCharges,
                          taxRate: paymentSummary.taxRate,
                          voucher: paymentSummary.voucher,
                          roundOff: paymentSummary.roundOff
                        }).tax
                      )}
                    </div>
                  </div>
                </div>
              )}

              <small className="text-muted">
                {loadingTaxRates ? (
                  <span><i className="ti ti-loader spin me-1"></i>Loading tax categories from API...</span>
                ) : (
                  <>
                    {taxCategories.length > 0 ? (
                      <>
                        <i className="ti ti-database me-1 text-success"></i>
                        Tax categories are synchronized with API.
                        <span className="text-success ms-2">
                          <i className="ti ti-check me-1"></i>
                          {taxCategories.length} tax categories loaded from API
                          {selectedTaxCategory && availableTaxRates.length > 0 && (
                            <span className="text-info ms-1">
                              ({availableTaxRates.length} tax rates available for {selectedTaxCategory.name})
                            </span>
                          )}
                        </span>
                      </>
                    ) : (
                      <>
                        <i className="ti ti-database-off me-1 text-warning"></i>
                        No tax categories loaded. API connection may be unavailable.
                        <button
                          type="button"
                          className="btn btn-sm btn-outline-primary ms-2"
                          onClick={loadTaxRatesFromAPI}
                          disabled={loadingTaxRates}
                        >
                          <i className="ti ti-refresh me-1"></i>
                          Retry API Connection
                        </button>
                      </>
                    )}
                  </>
                )}
              </small>
            </div>
          </div>
          <div className="modal-footer d-flex justify-content-end flex-wrap gap-2">
            <button
              type="button"
              className="btn btn-md btn-secondary"
              data-bs-dismiss="modal"
            >
              Cancel
            </button>
            <button type="submit" className="btn btn-md btn-primary">
              Submit
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
  {/* /Order Tax */}

  {/* Order Tax View (Read-Only) */}
  <div className="modal fade modal-default" id="order-tax-view">
    <div className="modal-dialog modal-dialog-centered modal-lg">
      <div className="modal-content">
        <div className="modal-header">
          <h5 className="modal-title">
            <i className="ti ti-eye me-2"></i>
            Tax Information (View Only)
          </h5>
          <button
            type="button"
            className="close"
            data-bs-dismiss="modal"
            aria-label="Close"
          >
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <div className="modal-body pb-1">
          {/* Company Type Information */}
          {companyTaxSettings && (
            <div className="mb-3">
              <div className={`alert ${companyTaxSettings.isServiceTaxCompany ? 'alert-info' : 'alert-light'} d-flex align-items-center py-2`}>
                <i className={`ti ${companyTaxSettings.isServiceTaxCompany ? 'ti-briefcase' : 'ti-building'} me-2`}></i>
                <div className="flex-grow-1">
                  <small>
                    <strong>Company Type: </strong>
                    {companyTaxSettings.isServiceTaxCompany ? 'Service Tax Company' : 'General Business Company'}
                  </small>
                </div>
              </div>
            </div>
          )}

          {/* Current Tax Information */}
          <div className="mb-4">
            <h6 className="mb-3">
              <i className="ti ti-info-circle me-2"></i>
              Current Tax Configuration
            </h6>

            {/* Current Tax Category */}
            {selectedTaxCategory ? (
              <div className="card border-primary bg-primary-light mb-3">
                <div className="card-body p-3">
                  <div className="d-flex justify-content-between align-items-start mb-2">
                    <h6 className="card-title mb-0 text-primary">
                      {selectedTaxCategory.displayName}
                    </h6>
                    <div className="d-flex flex-column gap-1">
                      {selectedTaxCategory.code === 'SALESTAX' && (
                        <span className="badge bg-success">Sales Tax</span>
                      )}
                      {selectedTaxCategory.code === 'SERVICETAX' && (
                        <span className="badge bg-info">Service Tax</span>
                      )}
                      {selectedTaxCategory.code === 'NOTAX' && (
                        <span className="badge bg-secondary">No Tax</span>
                      )}
                    </div>
                  </div>
                  <div className="text-primary fw-bold fs-6">
                    {selectedTaxCategory.hasRates ? (
                      `${selectedTaxCategory.taxRates.length} tax rates available`
                    ) : (
                      'No tax applicable'
                    )}
                  </div>
                  {selectedTaxCategory.description && (
                    <small className="text-muted d-block mt-1">
                      {selectedTaxCategory.description}
                    </small>
                  )}
                  <div className="mt-2">
                    <i className="ti ti-check-circle text-primary"></i>
                    <small className="text-primary ms-1">Currently Selected</small>
                  </div>
                </div>
              </div>
            ) : (
              <div className="alert alert-warning d-flex align-items-center mb-3">
                <i className="ti ti-alert-triangle me-2"></i>
                <div>
                  <strong>No tax category selected</strong>
                  <br />
                  <small>No tax category has been configured for this order.</small>
                </div>
              </div>
            )}

            {/* Current Tax Rate */}
            {selectedTaxRate ? (
              <div className="p-3 bg-light border border-success rounded" style={{backgroundColor: '#d4edda'}}>
                <div className="d-flex align-items-center justify-content-between">
                  <div className="d-flex align-items-center">
                    <i className="ti ti-check-circle text-success me-2"></i>
                    <div>
                      <h6 className="mb-1 text-success">Applied Tax Rate</h6>
                      <p className="mb-0 small text-muted">
                        {selectedTaxRate.code} - {selectedTaxRate.name}
                      </p>
                    </div>
                  </div>
                  <div className="text-end">
                    <span className="badge bg-success fs-6 px-3 py-2">
                      {selectedTaxRate.chargePercentage}%
                    </span>
                  </div>
                </div>
                {selectedTaxRate.description && (
                  <small className="text-muted d-block mt-2">
                    <i className="ti ti-info-circle me-1"></i>
                    {selectedTaxRate.description}
                  </small>
                )}
              </div>
            ) : paymentSummary.taxRate > 0 ? (
              <div className="p-3 bg-light border border-info rounded">
                <div className="d-flex align-items-center justify-content-between">
                  <div className="d-flex align-items-center">
                    <i className="ti ti-percentage text-info me-2"></i>
                    <div>
                      <h6 className="mb-1 text-info">Applied Tax Rate</h6>
                      <p className="mb-0 small text-muted">
                        Custom tax rate applied
                      </p>
                    </div>
                  </div>
                  <div className="text-end">
                    <span className="badge bg-info fs-6 px-3 py-2">
                      {paymentSummary.taxRate}%
                    </span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="alert alert-secondary d-flex align-items-center">
                <i className="ti ti-ban me-2"></i>
                <div>
                  <strong>No tax applied</strong>
                  <br />
                  <small>This order has no tax charges applied.</small>
                </div>
              </div>
            )}
          </div>

          {/* Tax Calculation Preview */}
          {paymentSummary.taxRate > 0 && (
            <div className="mb-3 p-3 bg-light rounded">
              <h6 className="mb-2">
                <i className="ti ti-calculator me-2"></i>
                Tax Calculation Details
              </h6>
              <div className="row g-2 small">
                <div className="col-6">
                  <strong>Applied Tax Rate:</strong>
                </div>
                <div className="col-6 text-end">
                  {paymentSummary.taxRate}%
                </div>
                <div className="col-6">
                  <strong>Tax Amount:</strong>
                </div>
                <div className="col-6 text-end text-primary fw-bold">
                  {formatCurrency(
                    calculateOrderTotal({
                      discountType: discountSettings.discountType,
                      discountValue: discountSettings.discountValue,
                      customPercentage: discountSettings.customPercentage,
                      serviceCharges: paymentSummary.serviceCharges,
                      taxRate: paymentSummary.taxRate,
                      voucher: paymentSummary.voucher,
                      roundOff: paymentSummary.roundOff
                    }).tax
                  )}
                </div>
                <div className="col-6">
                  <strong>Subtotal (before tax):</strong>
                </div>
                <div className="col-6 text-end">
                  {formatCurrency(
                    calculateOrderTotal({
                      discountType: discountSettings.discountType,
                      discountValue: discountSettings.discountValue,
                      customPercentage: discountSettings.customPercentage,
                      serviceCharges: paymentSummary.serviceCharges,
                      taxRate: 0, // Calculate without tax
                      voucher: paymentSummary.voucher,
                      roundOff: paymentSummary.roundOff
                    }).total
                  )}
                </div>
                <div className="col-12">
                  <hr className="my-2" />
                </div>
                <div className="col-6">
                  <strong>Total (including tax):</strong>
                </div>
                <div className="col-6 text-end fw-bold text-success">
                  {formatCurrency(
                    calculateOrderTotal({
                      discountType: discountSettings.discountType,
                      discountValue: discountSettings.discountValue,
                      customPercentage: discountSettings.customPercentage,
                      serviceCharges: paymentSummary.serviceCharges,
                      taxRate: paymentSummary.taxRate,
                      voucher: paymentSummary.voucher,
                      roundOff: paymentSummary.roundOff
                    }).total
                  )}
                </div>
              </div>
            </div>
          )}

          <small className="text-muted">
            <i className="ti ti-info-circle me-1"></i>
            This is a read-only view of the current tax configuration. Tax settings cannot be modified from this screen.
          </small>
        </div>
        <div className="modal-footer d-flex justify-content-end">
          <button
            type="button"
            className="btn btn-md btn-secondary"
            data-bs-dismiss="modal"
          >
            <i className="ti ti-x me-1"></i>
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
  {/* /Order Tax View */}

  {/* Service Charges - Removed as it's not editable */}
  {/* Voucher Code - only show if voucher is enabled */}
  {isVoucherEnabled() && (
  <div className="modal fade modal-default" id="coupon-code">
    <div className="modal-dialog modal-dialog-centered">
      <div className="modal-content">
        <div className="modal-header">
          <h5 className="modal-title">Voucher Code</h5>
          <button
            type="button"
            className="close"
            data-bs-dismiss="modal"
            aria-label="Close"
          >
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <form onSubmit={(e) => {
          e.preventDefault();

          // Get voucher amount
          const voucherAmount = parseFloat(e.target.voucherAmount.value) || 0;

          // Update payment summary
          const newPaymentSummary = { ...paymentSummary, voucher: voucherAmount };
          setPaymentSummary(newPaymentSummary);
          savePaymentSummary(newPaymentSummary);

          // Recalculate order total
          recalculateOrderTotal(discountSettings, newPaymentSummary);

          // Close modal
          document.querySelector('#coupon-code .close').click();
        }}>
          <div className="modal-body pb-1">
            <div className="mb-3">
              <label className="form-label">
                Voucher Code <span className="text-danger">*</span>
              </label>
              <Select
                options={options.couponCodes}
                classNamePrefix="react-select"
                placeholder="Select"
                defaultValue={options.couponCodes[0]}
              />
            </div>
            <div className="mb-3">
              <label className="form-label">
                Voucher Amount <span className="text-danger">*</span>
              </label>
              <input
                type="number"
                name="voucherAmount"
                className="form-control"
                defaultValue={paymentSummary.voucher}
                min="0"
                step="0.01"
              />
            </div>
          </div>
          <div className="modal-footer d-flex justify-content-end flex-wrap gap-2">
            <button
              type="button"
              className="btn btn-md btn-secondary"
              data-bs-dismiss="modal"
            >
              Cancel
            </button>
            <button type="submit" className="btn btn-md btn-primary">
              Submit
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
  )}
  {/* /Voucher Code */}
  {/* Discount */}
  <div className="modal fade modal-default" id="discount">
    <div className="modal-dialog modal-dialog-centered">
      <div className="modal-content">
        <div className="modal-header">
          <h5 className="modal-title">Discount </h5>
          <button
            type="button"
            className="close"
            data-bs-dismiss="modal"
            aria-label="Close"
          >
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <form onSubmit={(e) => {
          e.preventDefault();
          // Save discount settings
          saveDiscountSettings(discountSettings);

          // Recalculate order total
          recalculateOrderTotal(discountSettings, paymentSummary);

          // Close modal
          document.querySelector('#discount .close').click();
        }}>
          <div className="modal-body pb-1">
            <div className="mb-3">
              <label className="form-label">
                Order Discount Type <span className="text-danger">*</span>
              </label>
              <Select
                options={options.discountMode}
                classNamePrefix="react-select select"
                placeholder="Select"
                value={options.discountMode.find(option => option.value === discountSettings.discountType) || options.discountMode[0]}
                onChange={(selectedOption) => {
                  setDiscountSettings({
                    ...discountSettings,
                    discountType: selectedOption.value
                  });
                }}
              />
            </div>
            <div className="mb-3">
              <label className="form-label">
                Value <span className="text-danger">*</span>
              </label>
              <input
                type="text"
                className="form-control"
                value={discountSettings.discountValue}
                onChange={(e) => {
                  setDiscountSettings({
                    ...discountSettings,
                    discountValue: e.target.value
                  });
                }}
              />
            </div>
            <div className="mb-3">
              <label className="form-label">
                Custom Percentage <span className="text-info">(Optional)</span>
              </label>
              <div className="input-icon-start position-relative">
                <span className="input-icon-addon text-gray-9">
                  <i className="ti ti-percentage" />
                </span>
                <input
                  type="number"
                  className="form-control"
                  placeholder="Enter custom percentage discount"
                  min="0"
                  max="100"
                  value={discountSettings.customPercentage}
                  onChange={(e) => {
                    setDiscountSettings({
                      ...discountSettings,
                      customPercentage: e.target.value
                    });
                  }}
                />
              </div>
              <small className="text-muted">Enter a value between 0-100 to set a custom percentage discount</small>
            </div>
          </div>
          <div className="modal-footer d-flex justify-content-end flex-wrap gap-2">
            <button
              type="button"
              className="btn btn-md btn-secondary"
              data-bs-dismiss="modal"
            >
              Cancel
            </button>
            <button type="submit" className="btn btn-md btn-primary">
              Apply Discount
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
  {/* /Discount */}
  {/* Cash Payment */}
  <div
    className="modal fade modal-default"
    id="cash-payment"
    aria-labelledby="payment-completed"
  >
    <div className="modal-dialog modal-dialog-centered">
      <div className="modal-content">
        <div className="modal-body p-0">
          <div className="success-wrap">
            <div className="text-center">
              <div className="icon-success bg-success text-white mb-2">
                <i className="ti ti-check" />
              </div>
              <h3 className="mb-2">Congratulations, Sale Completed</h3>
              <div className="p-2 d-flex align-items-center justify-content-center gap-2 flex-wrap mb-3">
                <p className="fs-13 fw-medium pe-2 border-end mb-0">
                  Bill Amount : <span className="text-gray-9">$150</span>
                </p>
                <p className="fs-13 fw-medium pe-2 border-end mb-0">
                  Total Paid : <span className="text-gray-9">$200</span>
                </p>
                <p className="fs-13 fw-medium mb-0">
                  Change : <span className="text-gray-9">$50</span>
                </p>
              </div>
            </div>
            <div className="bg-success-transparent p-2 mb-3 br-5 border-start border-success d-flex align-items-center">
              <span className="avatar avatar-sm bg-success rounded-circle flex-shrink-0 fs-16 me-2">
                <i className="ti ti-mail-opened" />
              </span>
              <p className="fs-13 fw-medium text-gray-9">
                A receipt of this transaction will be sent to the registered
                info@<EMAIL>
              </p>
            </div>
            <div className="resend-form mb-3">
              <input
                type="text"
                className="form-control"
                defaultValue="<EMAIL>"
              />
              <button type="button" data-bs-dismiss="modal" className="btn btn-primary btn-xs">
                Resend Email
              </button>
            </div>
            <div className="d-flex align-items-center justify-content-center gap-2 flex-wrap">
              <button
                type="button"
                className="btn btn-md btn-light flex-fill"
                data-bs-toggle="modal"
                data-bs-target="#print-receipt"
              >
                <i className="ti ti-printer me-1" />
                Print Receipt
              </button>
              <button type="button" className="btn btn-md btn-teal flex-fill">
                <i className="ti ti-gift me-1" />
                Gift Receipt
              </button>
              <Link to="#" className="btn btn-md btn-dark flex-fill">
                <i className="ti ti-shopping-cart me-1" />
                Next Order
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  {/* /Cash Payment */}
  {/* Card Payment */}
  <div className="modal fade modal-default" id="card-payment">
    <div className="modal-dialog modal-dialog-centered">
      <div className="modal-content">
        <div className="modal-body p-0">
          <div className="success-wrap">
            <div className="text-center">
              <div className="icon-success bg-success text-white mb-2">
                <i className="ti ti-check" />
              </div>
              <h3 className="mb-2">Congratulations, Sale Completed</h3>
              <div className="p-2 text-center mb-3">
                <p className="fs-13 fw-medium">
                  Bill Amount : <span className="text-gray-9">$150</span>
                </p>
              </div>
            </div>
            <div className="bg-success-transparent p-2 mb-3 br-5 border-start border-success d-flex align-items-center">
              <span className="avatar avatar-sm bg-success rounded-circle flex-shrink-0 fs-16 me-2">
                <i className="ti ti-mail-opened" />
              </span>
              <p className="fs-13 fw-medium text-gray-9">
                A receipt of this transaction will be sent to the registered
                info@<EMAIL>
              </p>
            </div>
            <div className="resend-form mb-3">
              <input
                type="text"
                className="form-control"
                defaultValue="<EMAIL>"
              />
              <button type="button" data-bs-dismiss="modal" className="btn btn-primary btn-xs">
                Resend Email
              </button>
            </div>
            <div className="d-flex align-items-center justify-content-center gap-2 flex-wrap">
              <button
                type="button"
                className="btn btn-md btn-light flex-fill"
                data-bs-toggle="modal"
                data-bs-target="#print-receipt"
              >
                <i className="ti ti-printer me-1" />
                Print Receipt
              </button>
              <button type="button" className="btn btn-md btn-teal flex-fill">
                <i className="ti ti-gift me-1" />
                Gift Receipt
              </button>
              <Link to="#" className="btn btn-md btn-dark flex-fill">
                <i className="ti ti-shopping-cart me-1" />
                Next Order
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  {/* /Card Payment */}
  {/* Active Gift Card */}
  <div
    className="modal fade pos-modal"
    id="gift-payment"
    tabIndex={-1}
    aria-hidden="true"
  >
    <div
      className="modal-dialog modal-md modal-dialog-centered"
      role="document"
    >
      <div className="modal-content">
        <div className="modal-header">
          <h5 className="modal-title">Pay By Gift Card</h5>
          <button
            type="button"
            className="close"
            data-bs-dismiss="modal"
            aria-label="Close"
          >
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <div className="modal-body pb-1">
          <div className="mb-3">
            <ImageWithBasePath src="assets/img/icons/gift-card.svg" alt="img" />
          </div>
          <div className="resend-form mb-3">
            <input
              type="text"
              className="form-control"
              placeholder="Scan Barcode / Enter Number"
            />
            <button
              type="button" data-bs-dismiss="modal"
              className="btn btn-primary btn-xs"
              data-bs-toggle="modal"
              data-bs-target="#redeem-value"
            >
              Check
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  {/* /Active Gift Card */}
  {/* Redeem Value */}
  <div
    className="modal fade pos-modal"
    id="redeem-value"
    tabIndex={-1}
    aria-hidden="true"
  >
    <div
      className="modal-dialog modal-md modal-dialog-centered"
      role="document"
    >
      <div className="modal-content">
        <div className="modal-header">
          <h5 className="modal-title">Redeem Value</h5>
          <button
            type="button"
            className="close"
            data-bs-dismiss="modal"
            aria-label="Close"
          >
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <form >
          <div className="modal-body">
            <div className="bg-info-transparent p-2 br-8 mb-3">
              <p className="text-info">
                Balance isn’t enough to pay, you can still make a partial
                payment
              </p>
            </div>
            <div className="card bg-light shadow-none text-center">
              <div className="card-body">
                <p className="text-gray-5 mb-1">Gift Card Number</p>
                <h2 className="display-1">5698</h2>
              </div>
            </div>
            <div className="bg-danger-transparent p-2 mb-3 br-5 border-start border-danger d-flex align-items-center">
              <span className="avatar avatar-sm bg-danger rounded-circle fs-16 me-2">
                <i className="ti ti-gift" />
              </span>
              <p className="fs-16 text-gray-9">
                Available gift card balance : $45.56
              </p>
            </div>
            <div>
              <input
                type="text"
                className="form-control"
                placeholder="Enter Bill Amount"
              />
            </div>
          </div>
          <div className="modal-footer d-flex justify-content-end gap-2 flex-wrap">
            <button
              type="button"
              className="btn btn-md btn-secondary"
              data-bs-dismiss="modal"
            >
              Cancel
            </button>
            <button type="button" data-bs-dismiss="modal" className="btn btn-md btn-primary">
              Make Partial Payment
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
  {/* /Redeem Value */}
  {/* Redeem Value */}
  <div
    className="modal fade pos-modal"
    id="redeem-fullpayment"
    tabIndex={-1}
    aria-hidden="true"
  >
    <div
      className="modal-dialog modal-md modal-dialog-centered"
      role="document"
    >
      <div className="modal-content">
        <div className="modal-header">
          <h5 className="modal-title">Redeem Value</h5>
          <button
            type="button"
            className="close"
            data-bs-dismiss="modal"
            aria-label="Close"
          >
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <form >
          <div className="modal-body">
            <div className="card bg-light shadow-none text-center">
              <div className="card-body">
                <p className="text-gray-5 mb-1">Gift Card Number</p>
                <h2 className="display-1">5698</h2>
              </div>
            </div>
            <div className="bg-success-transparent p-2 mb-3 br-5 border-start border-success">
              <span className="avatar avatar-sm bg-success rounded-circle fs-16 me-2">
                <i className="ti ti-gift" />
              </span>
              <p className="fs-16 text-gray-9">
                Available gift card balance : $45.56
              </p>
            </div>
            <div>
              <input
                type="text"
                className="form-control"
                placeholder="Enter Bill Amount"
              />
            </div>
          </div>
          <div className="modal-footer d-flex justify-content-end gap-2 flex-wrap">
            <button
              type="button"
              className="btn btn-md btn-secondary"
              data-bs-dismiss="modal"
            >
              Cancel
            </button>
            <button type="button" data-bs-dismiss="modal" className="btn btn-md btn-primary">
              Make Payment
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
  {/* /Redeem Value */}
  {/* Barcode */}
  <div
    className="modal fade pos-modal"
    id="barcode"
    tabIndex={-1}
    aria-hidden="true"
  >
    <div
      className="modal-dialog modal-md modal-dialog-centered"
      role="document"
    >
      <div className="modal-content">
        <div className="modal-header">
          <h5 className="modal-title">Barcode</h5>
          <button
            type="button"
            className="close"
            data-bs-dismiss="modal"
            aria-label="Close"
          >
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <form >
          <div className="modal-body">
            <div className="mb-3">
              <input
                type="text"
                className="form-control"
                placeholder="Enter Barcode of the Product"
              />
            </div>
            <div className="card bg-light shadow-none border-0 br-0 mb-0">
              <div className="card-body d-flex align-items-center justify-content-between">
                <div>
                  <p className="fs-13 mb-1">Tablet 1.02 inch</p>
                  <h6 className="fs-13 fw-semibold">$3000</h6>
                </div>
                <div className="qty-item m-0">
                  <Link
                    to="#"
                    className="dec d-flex justify-content-center align-items-center"
                    data-bs-toggle="tooltip"
                    data-bs-placement="top"
                    title="minus"
                  >
                    <i className="ti ti-minus" />
                  </Link>
                  <input
                    type="text"
                    className="form-control text-center"
                    name="qty"
                    defaultValue={4}
                  />
                  <Link
                    to="#"
                    className="inc d-flex justify-content-center align-items-center"
                    data-bs-toggle="tooltip"
                    data-bs-placement="top"
                    title="plus"
                  >
                    <i className="ti ti-plus" />
                  </Link>
                </div>
              </div>
            </div>
          </div>
          <div className="modal-footer d-flex justify-content-end gap-2 flex-wrap">
            <button
              type="button"
              className="btn btn-md btn-secondary"
              data-bs-dismiss="modal"
            >
              Cancel
            </button>
            <button type="button" data-bs-dismiss="modal" className="btn btn-md btn-primary">
              Add Item
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
  {/* /Barcode */}
  {/* Split Payment */}
  <div
    className="modal fade pos-modal"
    id="split-payment"
    tabIndex={-1}
    aria-hidden="true"
  >
    <div
      className="modal-dialog modal-md modal-dialog-centered modal-lg"
      role="document"
    >
      <div className="modal-content">
        <div className="modal-header">
          <h5 className="modal-title">Split Payment</h5>
          <button
            type="button"
            className="close"
            data-bs-dismiss="modal"
            aria-label="Close"
          >
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <form >
          <div className="modal-body">
            <div className="additem-info">
              <div className="bg-light p-3 add-info">
                <div className="row align-items-center g-2">
                  <div className="col-lg-2">
                    <h6 className="fs-14 fw-semibold">Payment 2</h6>
                  </div>
                  <div className="col-lg-4">
                  <Select
                    options={options.paymentMethods}
                    classNamePrefix="react-select select"
                    placeholder="Select"
                    defaultValue={options.paymentMethods[0]}
                    />
                  </div>
                  <div className="col-lg-4">
                    <input
                      type="text"
                      className="form-control"
                      placeholder="Enter Amount"
                    />
                  </div>
                  <div className="col-lg-2">
                    <button className="btn btn-dark w-100">Charge</button>
                  </div>
                </div>
              </div>
              <div className="bg-light p-3 add-info">
                <div className="row align-items-center g-2">
                  <div className="col-lg-2">
                    <h6 className="fs-14 fw-semibold">Payment 2</h6>
                  </div>
                  <div className="col-lg-4">
                  <Select
                    options={options.paymentMethods}
                    classNamePrefix="react-select select"
                    placeholder="Select"
                    defaultValue={options.paymentMethods[0]}
                    />
                  </div>
                  <div className="col-lg-4">
                    <input
                      type="text"
                      className="form-control"
                      placeholder="Enter Amount"
                    />
                  </div>
                  <div className="col-lg-2">
                    <button className="btn btn-dark w-100">Charge</button>
                  </div>
                </div>
              </div>
            </div>
            <div className="text-end">
              <Link to="#" className="btn btn-primary btn-md add-item">
                Add More
              </Link>
            </div>
          </div>
          <div className="modal-footer d-flex justify-content-end gap-2 flex-wrap">
            <button
              type="button"
              className="btn btn-md btn-secondary"
              data-bs-dismiss="modal"
            >
              Cancel
            </button>
            <Link
              to="#"
              className="btn btn-md btn-primary"
              data-bs-dismiss="modal"
              data-bs-toggle="modal"
              data-bs-target="#cash-payment"
            >
              Complete Sale
            </Link>
          </div>
        </form>
      </div>
    </div>
  </div>
  {/* /Split Payment */}
  {/* Payment Cash */}
  <div className="modal fade modal-default" id="payment-cash">
    <div className="modal-dialog modal-dialog-centered modal-lg">
      <div className="modal-content">
        <div className="modal-header">
          <h5 className="modal-title">Finalize Sale</h5>
          <button
            type="button"
            className="close"
            data-bs-dismiss="modal"
            aria-label="Close"
          >
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <form onSubmit={handleCashPaymentSubmit}>
          <div className="modal-body pb-1">
            {/* Payment Balance Summary */}
            <div className="payment-balance-info mb-4 p-3 bg-info-transparent rounded">
              <div className="row g-3">
                <div className="col-md-4">
                  <div className="text-center">
                    <small className="text-muted d-block">Order Total</small>
                    <span className="fw-bold text-primary fs-5">
                      {formatPaymentCurrency(orderTotal)}
                    </span>
                  </div>
                </div>
                <div className="col-md-4">
                  <div className="text-center">
                    <small className="text-muted d-block">Already Paid</small>
                    <span className="fw-bold text-success">
                      {formatPaymentCurrency(calculateTotalPaid())}
                    </span>
                  </div>
                </div>
                <div className="col-md-4">
                  <div className="text-center">
                    <small className="text-muted d-block">Remaining</small>
                    <span className="fw-bold text-warning">
                      {formatPaymentCurrency(calculateRemainingBalance(orderTotal).remaining)}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className="row">
              <div className="col-md-4">
                <div className="mb-3">
                  <label className="form-label">
                    Received Amount <span className="text-danger">*</span>
                  </label>
                  <div className="input-icon-start position-relative">
                    <span className="input-icon-addon text-gray-9">
                      <i className="ti ti-currency-ringgit" />
                    </span>
                    <input
                      type="number"
                      step="0.01"
                      className="form-control"
                      value={cashPaymentForm.receivedAmount}
                      onChange={(e) => handleCashPaymentChange('receivedAmount', e.target.value)}
                      placeholder="0.00"
                      required
                    />
                  </div>
                </div>
              </div>
              <div className="col-md-4">
                <div className="mb-3">
                  <label className="form-label">
                    Paying Amount <span className="text-danger">*</span>
                  </label>
                  <div className="input-icon-start position-relative">
                    <span className="input-icon-addon text-gray-9">
                      <i className="ti ti-currency-ringgit" />
                    </span>
                    <input
                      type="number"
                      step="0.01"
                      className="form-control"
                      value={cashPaymentForm.payingAmount}
                      onChange={(e) => handleCashPaymentChange('payingAmount', e.target.value)}
                      placeholder="0.00"
                      required
                    />
                  </div>
                </div>
              </div>
              <div className="col-md-4">
                <div className="change-item mb-3">
                  <label className="form-label">Change</label>
                  <div className="input-icon-start position-relative">
                    <span className="input-icon-addon text-gray-9">
                      <i className="ti ti-currency-ringgit" />
                    </span>
                    <input
                      type="text"
                      className="form-control"
                      value={formatPaymentCurrency(cashPaymentForm.change)}
                      readOnly
                    />
                  </div>
                </div>
                {/* <div className="point-item mb-3">
                  <label className="form-label">Balance Point</label>
                  <input
                    type="text"
                    className="form-control"
                    defaultValue={200}
                  />
                </div> */}
              </div>
              <div className="col-md-12">
                <div className="mb-3">
                  <label className="form-label">
                    Payment Type <span className="text-danger">*</span>
                  </label>

                  <Select
                    options={options.paymentTypes}
                    classNamePrefix="react-select select"
                    placeholder="Select"
                    defaultValue={options.paymentTypes[1]}
                    />
                </div>
                <div className="quick-cash payment-content bg-light d-block mb-3">
                  <div className="d-flex align-items-center flex-wra gap-4">
                    <h5 className="text-nowrap">Quick Cash</h5>
                    <div className="d-flex align-items-center flex-wrap gap-3">
                      {[10, 20, 50, 100, 500, 1000].map((amount) => (
                        <button
                          key={amount}
                          type="button"
                          className="btn btn-white"
                          onClick={() => handleQuickCashSelect(amount)}
                        >
                          {amount}
                        </button>
                      ))}
                      <button
                        type="button"
                        className="btn btn-primary"
                        onClick={() => {
                          const remaining = calculateRemainingBalance(orderTotal).remaining;
                          handleQuickCashSelect(remaining);
                        }}
                      >
                        Exact
                      </button>
                    </div>
                  </div>
                </div>
                <div className="point-wrap payment-content mb-3">
                  <div className=" bg-success-transparent d-flex align-items-center justify-content-between flex-wrap p-2 gap-2 br-5">
                    <h6 className="fs-14 fw-bold text-success">
                      You have 2000 Points to Use
                    </h6>
                    <Link
                      to="#"
                      className="btn btn-dark btn-md"
                    >
                      Use for this Purchase
                    </Link>
                  </div>
                </div>
              </div>
              <div className="col-md-12">
                <div className="mb-3">
                  <label className="form-label">Payment Receiver</label>
                  <input
                    type="text"
                    className="form-control"
                    value={cashPaymentForm.paymentReceiver}
                    onChange={(e) => handleCashPaymentChange('paymentReceiver', e.target.value)}
                    placeholder="Enter receiver name"
                  />
                </div>
              </div>
              <div className="col-md-12">
                <div className="mb-3">
                  <label className="form-label">Payment Note</label>
                  <textarea
                    className="form-control"
                    rows={3}
                    placeholder="Type your message"
                    value={cashPaymentForm.paymentNote}
                    onChange={(e) => handleCashPaymentChange('paymentNote', e.target.value)}
                  />
                </div>
              </div>
              <div className="col-md-12">
                <div className="mb-3">
                  <label className="form-label">Sale Note</label>
                  <textarea
                    className="form-control"
                    rows={3}
                    placeholder="Type your message"
                    value={cashPaymentForm.saleNote}
                    onChange={(e) => handleCashPaymentChange('saleNote', e.target.value)}
                  />
                </div>
              </div>
              <div className="col-md-12">
                <div className="mb-3">
                  <label className="form-label">Staff Note</label>
                  <textarea
                    className="form-control"
                    rows={3}
                    placeholder="Type your message"
                    value={cashPaymentForm.staffNote}
                    onChange={(e) => handleCashPaymentChange('staffNote', e.target.value)}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="modal-footer d-flex justify-content-end flex-wrap gap-2">
            <button
              type="button"
              className="btn btn-md btn-secondary"
              data-bs-dismiss="modal"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn-md btn-primary"
              disabled={!cashPaymentForm.receivedAmount || !cashPaymentForm.payingAmount || parseFloat(cashPaymentForm.payingAmount) <= 0}
            >
              Add Payment
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
  {/* /Payment Cash  */}
  {/* Payment Card  */}
  <div className="modal fade modal-default" id="payment-card">
    <div className="modal-dialog modal-dialog-centered modal-lg">
      <div className="modal-content">
        <div className="modal-header">
          <h5 className="modal-title">Finalize Sale</h5>
          <button
            type="button"
            className="close"
            data-bs-dismiss="modal"
            aria-label="Close"
          >
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <form >
          <div className="modal-body pb-1">
            <div className="row">
              <div className="col-md-4">
                <div className="mb-3">
                  <label className="form-label">
                    Received Amount <span className="text-danger">*</span>
                  </label>
                  <div className="input-icon-start position-relative">
                    <span className="input-icon-addon text-gray-9">
                      <i className="ti ti-currency-dollar" />
                    </span>
                    <input
                      type="text"
                      className="form-control"
                      defaultValue={1800}
                    />
                  </div>
                </div>
              </div>
              <div className="col-md-4">
                <div className="mb-3">
                  <label className="form-label">
                    Paying Amount <span className="text-danger">*</span>
                  </label>
                  <div className="input-icon-start position-relative">
                    <span className="input-icon-addon text-gray-9">
                      <i className="ti ti-currency-dollar" />
                    </span>
                    <input
                      type="text"
                      className="form-control"
                      defaultValue={1800}
                    />
                  </div>
                </div>
              </div>
              <div className="col-md-4">
                <div className="change-item mb-3">
                  <label className="form-label">Change</label>
                  <div className="input-icon-start position-relative">
                    <span className="input-icon-addon text-gray-9">
                      <i className="ti ti-currency-dollar" />
                    </span>
                    <input
                      type="text"
                      className="form-control"
                      defaultValue={0.0}
                    />
                  </div>
                </div>
                <div className="point-item mb-3">
                  <label className="form-label">Balance Point</label>
                  <input
                    type="text"
                    className="form-control"
                    defaultValue={200}
                  />
                </div>
              </div>
              <div className="col-md-12">
                <div className="mb-3">
                  <label className="form-label">
                    Payment Type <span className="text-danger">*</span>
                  </label>

                  <Select
                    options={options.paymentTypes}
                    classNamePrefix="react-select select"
                    placeholder="Select"
                    defaultValue={options.paymentTypes[0]}
                    />
                </div>
                <div className="quick-cash payment-content bg-light  mb-3">
                  <div className="d-flex align-items-center flex-wra gap-4">
                    <h5 className="text-nowrap">Quick Cash</h5>
                    <div className="d-flex align-items-center flex-wrap gap-3">
                      <div className="form-check">
                        <input
                          type="radio"
                          className="btn-check"
                          name="cash"
                          id="cash11"
                          defaultChecked
                        />
                        <label className="btn btn-white" htmlFor="cash11">
                          10
                        </label>
                      </div>
                      <div className="form-check">
                        <input
                          type="radio"
                          className="btn-check"
                          name="cash"
                          id="cash12"
                        />
                        <label className="btn btn-white" htmlFor="cash12">
                          20
                        </label>
                      </div>
                      <div className="form-check">
                        <input
                          type="radio"
                          className="btn-check"
                          name="cash"
                          id="cash13"
                        />
                        <label className="btn btn-white" htmlFor="cash13">
                          50
                        </label>
                      </div>
                      <div className="form-check">
                        <input
                          type="radio"
                          className="btn-check"
                          name="cash"
                          id="cash14"
                        />
                        <label className="btn btn-white" htmlFor="cash14">
                          100
                        </label>
                      </div>
                      <div className="form-check">
                        <input
                          type="radio"
                          className="btn-check"
                          name="cash"
                          id="cash15"
                        />
                        <label className="btn btn-white" htmlFor="cash15">
                          500
                        </label>
                      </div>
                      <div className="form-check">
                        <input
                          type="radio"
                          className="btn-check"
                          name="cash"
                          id="cash16"
                        />
                        <label className="btn btn-white" htmlFor="cash16">
                          1000
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="point-wrap payment-content mb-3">
                  <div className=" bg-success-transparent d-flex align-items-center justify-content-between flex-wrap p-2 gap-2 br-5">
                    <h6 className="fs-14 fw-bold text-success">
                      You have 2000 Points to Use
                    </h6>
                    <Link
                      to="#"
                      className="btn btn-dark btn-md"
                    >
                      Use for this Purchase
                    </Link>
                  </div>
                </div>
              </div>
              <div className="col-md-12">
                <div className="mb-3">
                  <label className="form-label">Payment Receiver</label>
                  <input type="text" className="form-control" />
                </div>
              </div>
              <div className="col-md-12">
                <div className="mb-3">
                  <label className="form-label">Payment Note</label>
                  <textarea
                    className="form-control"
                    rows={3}
                    placeholder="Type your message"
                    defaultValue={""}
                  />
                </div>
              </div>
              <div className="col-md-12">
                <div className="mb-3">
                  <label className="form-label">Sale Note</label>
                  <textarea
                    className="form-control"
                    rows={3}
                    placeholder="Type your message"
                    defaultValue={""}
                  />
                </div>
              </div>
              <div className="col-md-12">
                <div className="mb-3">
                  <label className="form-label">Staff Note</label>
                  <textarea
                    className="form-control"
                    rows={3}
                    placeholder="Type your message"
                    defaultValue={""}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="modal-footer d-flex justify-content-end flex-wrap gap-2">
            <button
              type="button"
              className="btn btn-md btn-secondary"
              data-bs-dismiss="modal"
            >
              Cancel
            </button>
            <button type="button" data-bs-dismiss="modal" className="btn btn-md btn-primary">
              Submit
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
  {/* /Payment Card  */}
  {/* Payment Cheque */}
  <div className="modal fade modal-default" id="payment-cheque">
    <div className="modal-dialog modal-dialog-centered modal-lg">
      <div className="modal-content">
        <div className="modal-header">
          <h5 className="modal-title">Finalize Sale</h5>
          <button
            type="button"
            className="close"
            data-bs-dismiss="modal"
            aria-label="Close"
          >
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <form >
          <div className="modal-body pb-1">
            <div className="row">
              <div className="col-md-4">
                <div className="mb-3">
                  <label className="form-label">
                    Received Amount <span className="text-danger">*</span>
                  </label>
                  <div className="input-icon-start position-relative">
                    <span className="input-icon-addon text-gray-9">
                      <i className="ti ti-currency-dollar" />
                    </span>
                    <input
                      type="text"
                      className="form-control"
                      defaultValue={1800}
                    />
                  </div>
                </div>
              </div>
              <div className="col-md-4">
                <div className="mb-3">
                  <label className="form-label">
                    Paying Amount <span className="text-danger">*</span>
                  </label>
                  <div className="input-icon-start position-relative">
                    <span className="input-icon-addon text-gray-9">
                      <i className="ti ti-currency-dollar" />
                    </span>
                    <input
                      type="text"
                      className="form-control"
                      defaultValue={1800}
                    />
                  </div>
                </div>
              </div>
              <div className="col-md-4">
                <div className="change-item mb-3">
                  <label className="form-label">Change</label>
                  <div className="input-icon-start position-relative">
                    <span className="input-icon-addon text-gray-9">
                      <i className="ti ti-currency-dollar" />
                    </span>
                    <input
                      type="text"
                      className="form-control"
                      defaultValue={0.0}
                    />
                  </div>
                </div>
                {/* <div className="point-item mb-3">
                  <label className="form-label">Balance Point</label>
                  <input
                    type="text"
                    className="form-control"
                    defaultValue={200}
                  />
                </div> */}
              </div>
              <div className="col-md-12">
                <div className="mb-3">
                  <label className="form-label">
                    Payment Type <span className="text-danger">*</span>
                  </label>
                  <Select
                    options={options.paymentTypes}
                    classNamePrefix="react-select select"
                    placeholder="Select"
                    defaultValue={options.paymentTypes[2]}
                    />
                </div>
                <div className="quick-cash payment-content bg-light  mb-3">
                  <div className="d-flex align-items-center flex-wra gap-4">
                    <h5 className="text-nowrap">Quick Cash</h5>
                    <div className="d-flex align-items-center flex-wrap gap-3">
                      <div className="form-check">
                        <input
                          type="radio"
                          className="btn-check"
                          name="cash"
                          id="cash21"
                          defaultChecked
                        />
                        <label className="btn btn-white" htmlFor="cash21">
                          10
                        </label>
                      </div>
                      <div className="form-check">
                        <input
                          type="radio"
                          className="btn-check"
                          name="cash"
                          id="cash22"
                        />
                        <label className="btn btn-white" htmlFor="cash22">
                          20
                        </label>
                      </div>
                      <div className="form-check">
                        <input
                          type="radio"
                          className="btn-check"
                          name="cash"
                          id="cash23"
                        />
                        <label className="btn btn-white" htmlFor="cash23">
                          50
                        </label>
                      </div>
                      <div className="form-check">
                        <input
                          type="radio"
                          className="btn-check"
                          name="cash"
                          id="cash24"
                        />
                        <label className="btn btn-white" htmlFor="cash24">
                          100
                        </label>
                      </div>
                      <div className="form-check">
                        <input
                          type="radio"
                          className="btn-check"
                          name="cash"
                          id="cash25"
                        />
                        <label className="btn btn-white" htmlFor="cash25">
                          500
                        </label>
                      </div>
                      <div className="form-check">
                        <input
                          type="radio"
                          className="btn-check"
                          name="cash"
                          id="cash26"
                        />
                        <label className="btn btn-white" htmlFor="cash26">
                          1000
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="point-wrap payment-content mb-3">
                  <div className=" bg-success-transparent d-flex align-items-center justify-content-between flex-wrap p-2 gap-2 br-5">
                    <h6 className="fs-14 fw-bold text-success">
                      You have 2000 Points to Use
                    </h6>
                    <Link
                      to="#"
                      className="btn btn-dark btn-md"
                    >
                      Use for this Purchase
                    </Link>
                  </div>
                </div>
              </div>
              <div className="col-md-12">
                <div className="mb-3">
                  <label className="form-label">Payment Receiver</label>
                  <input type="text" className="form-control" />
                </div>
              </div>
              <div className="col-md-12">
                <div className="mb-3">
                  <label className="form-label">Payment Note</label>
                  <textarea
                    className="form-control"
                    rows={3}
                    placeholder="Type your message"
                    defaultValue={""}
                  />
                </div>
              </div>
              <div className="col-md-12">
                <div className="mb-3">
                  <label className="form-label">Sale Note</label>
                  <textarea
                    className="form-control"
                    rows={3}
                    placeholder="Type your message"
                    defaultValue={""}
                  />
                </div>
              </div>
              <div className="col-md-12">
                <div className="mb-3">
                  <label className="form-label">Staff Note</label>
                  <textarea
                    className="form-control"
                    rows={3}
                    placeholder="Type your message"
                    defaultValue={""}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="modal-footer d-flex justify-content-end flex-wrap gap-2">
            <button
              type="button"
              className="btn btn-md btn-secondary"
              data-bs-dismiss="modal"
            >
              Cancel
            </button>
            <button type="button" data-bs-dismiss="modal" className="btn btn-md btn-primary">
              Submit
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
  {/* /Payment Cheque */}
  {/*  Payment Deposit */}
  <div className="modal fade modal-default" id="payment-deposit">
    <div className="modal-dialog modal-dialog-centered modal-lg">
      <div className="modal-content">
        <div className="modal-header">
          <h5 className="modal-title">Finalize Sale</h5>
          <button
            type="button"
            className="close"
            data-bs-dismiss="modal"
            aria-label="Close"
          >
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <form >
          <div className="modal-body pb-1">
            <div className="row">
              <div className="col-md-4">
                <div className="mb-3">
                  <label className="form-label">
                    Received Amount <span className="text-danger">*</span>
                  </label>
                  <div className="input-icon-start position-relative">
                    <span className="input-icon-addon text-gray-9">
                      <i className="ti ti-currency-dollar" />
                    </span>
                    <input
                      type="text"
                      className="form-control"
                      defaultValue={1800}
                    />
                  </div>
                </div>
              </div>
              <div className="col-md-4">
                <div className="mb-3">
                  <label className="form-label">
                    Paying Amount <span className="text-danger">*</span>
                  </label>
                  <div className="input-icon-start position-relative">
                    <span className="input-icon-addon text-gray-9">
                      <i className="ti ti-currency-dollar" />
                    </span>
                    <input
                      type="text"
                      className="form-control"
                      defaultValue={1800}
                    />
                  </div>
                </div>
              </div>
              <div className="col-md-4">
                <div className="change-item mb-3">
                  <label className="form-label">Change</label>
                  <div className="input-icon-start position-relative">
                    <span className="input-icon-addon text-gray-9">
                      <i className="ti ti-currency-dollar" />
                    </span>
                    <input
                      type="text"
                      className="form-control"
                      defaultValue={0.0}
                    />
                  </div>
                </div>
                {/* <div className="point-item mb-3">
                  <label className="form-label">Balance Point</label>
                  <input
                    type="text"
                    className="form-control"
                    defaultValue={200}
                  />
                </div> */}
              </div>
              <div className="col-md-12">
                <div className="mb-3">
                  <label className="form-label">
                    Payment Type <span className="text-danger">*</span>
                  </label>
                  <Select
                    options={options.paymentTypes}
                    classNamePrefix="react-select select"
                    placeholder="Select"
                    defaultValue={options.paymentTypes[3]}
                    />
                </div>
                <div className="quick-cash payment-content bg-light  mb-3">
                  <div className="d-flex align-items-center flex-wra gap-4">
                    <h5 className="text-nowrap">Quick Cash</h5>
                    <div className="d-flex align-items-center flex-wrap gap-3">
                      <div className="form-check">
                        <input
                          type="radio"
                          className="btn-check"
                          name="cash"
                          id="cash31"
                          defaultChecked
                        />
                        <label className="btn btn-white" htmlFor="cash31">
                          10
                        </label>
                      </div>
                      <div className="form-check">
                        <input
                          type="radio"
                          className="btn-check"
                          name="cash"
                          id="cash32"
                        />
                        <label className="btn btn-white" htmlFor="cash32">
                          20
                        </label>
                      </div>
                      <div className="form-check">
                        <input
                          type="radio"
                          className="btn-check"
                          name="cash"
                          id="cash33"
                        />
                        <label className="btn btn-white" htmlFor="cash33">
                          50
                        </label>
                      </div>
                      <div className="form-check">
                        <input
                          type="radio"
                          className="btn-check"
                          name="cash"
                          id="cash34"
                        />
                        <label className="btn btn-white" htmlFor="cash34">
                          100
                        </label>
                      </div>
                      <div className="form-check">
                        <input
                          type="radio"
                          className="btn-check"
                          name="cash"
                          id="cash35"
                        />
                        <label className="btn btn-white" htmlFor="cash35">
                          500
                        </label>
                      </div>
                      <div className="form-check">
                        <input
                          type="radio"
                          className="btn-check"
                          name="cash"
                          id="cash36"
                        />
                        <label className="btn btn-white" htmlFor="cash36">
                          1000
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="point-wrap payment-content mb-3">
                  <div className=" bg-success-transparent d-flex align-items-center justify-content-between flex-wrap p-2 gap-2 br-5">
                    <h6 className="fs-14 fw-bold text-success">
                      You have 2000 Points to Use
                    </h6>
                    <Link
                      to="#"
                      className="btn btn-dark btn-md"
                    >
                      Use for this Purchase
                    </Link>
                  </div>
                </div>
              </div>
              <div className="col-md-12">
                <div className="mb-3">
                  <label className="form-label">Payment Receiver</label>
                  <input type="text" className="form-control" />
                </div>
              </div>
              <div className="col-md-12">
                <div className="mb-3">
                  <label className="form-label">Payment Note</label>
                  <textarea
                    className="form-control"
                    rows={3}
                    placeholder="Type your message"
                    defaultValue={""}
                  />
                </div>
              </div>
              <div className="col-md-12">
                <div className="mb-3">
                  <label className="form-label">Sale Note</label>
                  <textarea
                    className="form-control"
                    rows={3}
                    placeholder="Type your message"
                    defaultValue={""}
                  />
                </div>
              </div>
              <div className="col-md-12">
                <div className="mb-3">
                  <label className="form-label">Staff Note</label>
                  <textarea
                    className="form-control"
                    rows={3}
                    placeholder="Type your message"
                    defaultValue={""}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="modal-footer d-flex justify-content-end flex-wrap gap-2">
            <button
              type="button"
              className="btn btn-md btn-secondary"
              data-bs-dismiss="modal"
            >
              Cancel
            </button>
            <button type="button" data-bs-dismiss="modal" className="btn btn-md btn-primary">
              Submit
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
  {/* /Payment Deposit */}
  {/* Payment Point */}
  <div className="modal fade modal-default" id="payment-points">
    <div className="modal-dialog modal-dialog-centered modal-lg">
      <div className="modal-content">
        <div className="modal-header">
          <h5 className="modal-title">Finalize Sale</h5>
          <button
            type="button"
            className="close"
            data-bs-dismiss="modal"
            aria-label="Close"
          >
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <form >
          <div className="modal-body pb-1">
            <div className="row">
              <div className="col-md-4">
                <div className="mb-3">
                  <label className="form-label">
                    Received Amount <span className="text-danger">*</span>
                  </label>
                  <div className="input-icon-start position-relative">
                    <span className="input-icon-addon text-gray-9">
                      <i className="ti ti-currency-dollar" />
                    </span>
                    <input
                      type="text"
                      className="form-control"
                      defaultValue={1800}
                    />
                  </div>
                </div>
              </div>
              <div className="col-md-4">
                <div className="mb-3">
                  <label className="form-label">
                    Paying Amount <span className="text-danger">*</span>
                  </label>
                  <div className="input-icon-start position-relative">
                    <span className="input-icon-addon text-gray-9">
                      <i className="ti ti-currency-dollar" />
                    </span>
                    <input
                      type="text"
                      className="form-control"
                      defaultValue={1800}
                    />
                  </div>
                </div>
              </div>
              <div className="col-md-4">
                {/* <div className="change-item mb-3">
                  <label className="form-label">Change</label>
                  <div className="input-icon-start position-relative">
                    <span className="input-icon-addon text-gray-9">
                      <i className="ti ti-currency-dollar" />
                    </span>
                    <input
                      type="text"
                      className="form-control"
                      defaultValue={0.0}
                    />
                  </div>
                </div> */}
                <div className="point-item mb-3">
                  <label className="form-label">Balance Point</label>
                  <input
                    type="text"
                    className="form-control"
                    defaultValue={200}
                  />
                </div>
              </div>
              <div className="col-md-12">
                <div className="mb-3">
                  <label className="form-label">
                    Payment Type <span className="text-danger">*</span>
                  </label>
                  <Select
                    options={options.paymentTypes}
                    classNamePrefix="react-select select"
                    placeholder="Select"
                    defaultValue={options.paymentTypes[4]}
                    />
                </div>
                <div className="quick-cash payment-content bg-light  mb-3">
                  <div className="d-flex align-items-center flex-wra gap-4">
                    <h5 className="text-nowrap">Quick Cash</h5>
                    <div className="d-flex align-items-center flex-wrap gap-3">
                      <div className="form-check">
                        <input
                          type="radio"
                          className="btn-check"
                          name="cash"
                          id="cash41"
                          defaultChecked
                        />
                        <label className="btn btn-white" htmlFor="cash41">
                          10
                        </label>
                      </div>
                      <div className="form-check">
                        <input
                          type="radio"
                          className="btn-check"
                          name="cash"
                          id="cash42"
                        />
                        <label className="btn btn-white" htmlFor="cash42">
                          20
                        </label>
                      </div>
                      <div className="form-check">
                        <input
                          type="radio"
                          className="btn-check"
                          name="cash"
                          id="cash43"
                        />
                        <label className="btn btn-white" htmlFor="cash43">
                          50
                        </label>
                      </div>
                      <div className="form-check">
                        <input
                          type="radio"
                          className="btn-check"
                          name="cash"
                          id="cash44"
                        />
                        <label className="btn btn-white" htmlFor="cash44">
                          100
                        </label>
                      </div>
                      <div className="form-check">
                        <input
                          type="radio"
                          className="btn-check"
                          name="cash"
                          id="cash45"
                        />
                        <label className="btn btn-white" htmlFor="cash45">
                          500
                        </label>
                      </div>
                      <div className="form-check">
                        <input
                          type="radio"
                          className="btn-check"
                          name="cash"
                          id="cash46"
                        />
                        <label className="btn btn-white" htmlFor="cash46">
                          1000
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="point-wrap payment-content d-block mb-3">
                  <div className=" bg-success-transparent d-flex align-items-center justify-content-between flex-wrap p-2 gap-2 br-5">
                    <h6 className="fs-14 fw-bold text-success">
                      You have 2000 Points to Use
                    </h6>
                    <Link
                      to="#"
                      className="btn btn-dark btn-md"
                    >
                      Use for this Purchase
                    </Link>
                  </div>
                </div>
              </div>
              <div className="col-md-12">
                <div className="mb-3">
                  <label className="form-label">Payment Receiver</label>
                  <input type="text" className="form-control" />
                </div>
              </div>
              <div className="col-md-12">
                <div className="mb-3">
                  <label className="form-label">Payment Note</label>
                  <textarea
                    className="form-control"
                    rows={3}
                    placeholder="Type your message"
                    defaultValue={""}
                  />
                </div>
              </div>
              <div className="col-md-12">
                <div className="mb-3">
                  <label className="form-label">Sale Note</label>
                  <textarea
                    className="form-control"
                    rows={3}
                    placeholder="Type your message"
                    defaultValue={""}
                  />
                </div>
              </div>
              <div className="col-md-12">
                <div className="mb-3">
                  <label className="form-label">Staff Note</label>
                  <textarea
                    className="form-control"
                    rows={3}
                    placeholder="Type your message"
                    defaultValue={""}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="modal-footer d-flex justify-content-end flex-wrap gap-2">
            <button
              type="button"
              className="btn btn-md btn-secondary"
              data-bs-dismiss="modal"
            >
              Cancel
            </button>
            <button type="button" data-bs-dismiss="modal" className="btn btn-md btn-primary">
              Submit
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
  {/* /Payment Point */}
  {/* Calculator */}
  <div
    className="modal fade pos-modal"
    id="calculator"
    tabIndex={-1}
    aria-hidden="true"
  >
    <div
      className="modal-dialog modal-md modal-dialog-centered"
      role="document"
    >
      <div className="modal-content">
        <div className="modal-body p-0" onKeyDown={handleKeyPress} tabIndex="0">
          <div className="calculator-wrap">
            <div className="p-3">
              <div className="d-flex align-items-center">
                <h3>Calculator</h3>
                <button
                  type="button"
                  className="close"
                  data-bs-dismiss="modal"
                  aria-label="Close"
                >
                  <span aria-hidden="true">×</span>
                </button>
              </div>
              <div>
              <input
                className="input"
                type="text"
                placeholder="0"
                value={input}
                readOnly
            />
              </div>
            </div>
            <div className="calculator-body d-flex justify-content-between">
        {/* Column 1 */}
        <div className="text-center">
          <button className="btn btn-clear" onClick={handleClear}>C</button>
          <button className="btn btn-number" onClick={() => handleButtonClick("7")}>7</button>
          <button className="btn btn-number" onClick={() => handleButtonClick("4")}>4</button>
          <button className="btn btn-number" onClick={() => handleButtonClick("1")}>1</button>
          <button className="btn btn-number" onClick={() => handleButtonClick(",")}>,</button>
        </div>

        {/* Column 2 */}
        <div className="text-center">
          <button className="btn btn-expression" onClick={() => handleButtonClick("/")}>÷</button>
          <button className="btn btn-number" onClick={() => handleButtonClick("8")}>8</button>
          <button className="btn btn-number" onClick={() => handleButtonClick("5")}>5</button>
          <button className="btn btn-number" onClick={() => handleButtonClick("2")}>2</button>
          <button className="btn btn-number" onClick={() => handleButtonClick("00")}>00</button>
        </div>

        {/* Column 3 */}
        <div className="text-center">
          <button className="btn btn-expression" onClick={() => handleButtonClick("%")}>%</button>
          <button className="btn btn-number" onClick={() => handleButtonClick("9")}>9</button>
          <button className="btn btn-number" onClick={() => handleButtonClick("6")}>6</button>
          <button className="btn btn-number" onClick={() => handleButtonClick("3")}>3</button>
          <button className="btn btn-number" onClick={() => handleButtonClick(".")}>.</button>
        </div>

        {/* Column 4 */}
        <div className="text-center">
          <button className="btn btn-clear" onClick={handleBackspace}>
            <i className="ti ti-backspace" />
          </button>
          <button className="btn btn-expression" onClick={() => handleButtonClick("*")}>x</button>
          <button className="btn btn-expression" onClick={() => handleButtonClick("-")}>-</button>
          <button className="btn btn-expression" onClick={() => handleButtonClick("+")}>+</button>
          <button className="btn btn-clear" onClick={handleSolve}>=</button>
        </div>
      </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  {/* /Calculator */}
  {/* Cash Register Details */}
  <div
    className="modal fade pos-modal"
    id="cash-register"
    tabIndex={-1}
    aria-hidden="true"
  >
    <div
      className="modal-dialog modal-md modal-dialog-centered"
      role="document"
    >
      <div className="modal-content">
        <div className="modal-header">
          <h5 className="modal-title">Cash Register Details</h5>
          <button
            type="button"
            className="close"
            data-bs-dismiss="modal"
            aria-label="Close"
          >
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <div className="modal-body">
          <div className="table-responsive">
            <table className="table table-striped border">
              <tbody>
                <tr>
                  <td>Cash in Hand</td>
                  <td className="text-gray-9 fw-medium text-end">$45689</td>
                </tr>
                <tr>
                  <td>Total Sale Amount</td>
                  <td className="text-gray-9 fw-medium text-end">$565597.88</td>
                </tr>
                <tr>
                  <td>Total Payment</td>
                  <td className="text-gray-9 fw-medium text-end">$566867.97</td>
                </tr>
                <tr>
                  <td>Cash Payment</td>
                  <td className="text-gray-9 fw-medium text-end">$3355.84</td>
                </tr>
                <tr>
                  <td>Total Sale Return</td>
                  <td className="text-gray-9 fw-medium text-end">$1959</td>
                </tr>
                <tr>
                  <td>Total Expense</td>
                  <td className="text-gray-9 fw-medium text-end">$0</td>
                </tr>
                <tr>
                  <td className="text-gray-9 fw-bold bg-secondary-transparent">
                    Total Cash
                  </td>
                  <td className="text-gray-9 fw-bold text-end bg-secondary-transparent">
                    $587130.97
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div className="modal-footer d-flex justify-content-end gap-2 flex-wrap">
          <button
            type="button"
            className="btn btn-md btn-primary"
            data-bs-dismiss="modal"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
  {/* /Cash Register Details */}
  {/* Today&apos;s Sale */}
  <div
    className="modal fade pos-modal"
    id="today-sale"
    tabIndex={-1}
    aria-hidden="true"
  >
    <div
      className="modal-dialog modal-md modal-dialog-centered"
      role="document"
    >
      <div className="modal-content">
        <div className="modal-header">
          <h5 className="modal-title">Today&apos;s Sale</h5>
          <button
            type="button"
            className="close"
            data-bs-dismiss="modal"
            aria-label="Close"
          >
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <div className="modal-body">
          <div className="table-responsive">
            <table className="table table-striped border">
              <tbody>
                <tr>
                  <td>Total Sale Amount</td>
                  <td className="text-gray-9 fw-medium text-end">$565597.88</td>
                </tr>
                <tr>
                  <td>Cash Payment</td>
                  <td className="text-gray-9 fw-medium text-end">$3355.84</td>
                </tr>
                <tr>
                  <td>Credit Card Payment</td>
                  <td className="text-gray-9 fw-medium text-end">$1959</td>
                </tr>
                <tr>
                  <td>Cheque Payment:</td>
                  <td className="text-gray-9 fw-medium text-end">$0</td>
                </tr>
                <tr>
                  <td>Deposit Payment</td>
                  <td className="text-gray-9 fw-medium text-end">$565597.88</td>
                </tr>
                <tr>
                  <td>Points Payment</td>
                  <td className="text-gray-9 fw-medium text-end">$3355.84</td>
                </tr>
                <tr>
                  <td>Gift Card Payment</td>
                  <td className="text-gray-9 fw-medium text-end">$565597.88</td>
                </tr>
                <tr>
                  <td>Scan &amp; Pay</td>
                  <td className="text-gray-9 fw-medium text-end">$3355.84</td>
                </tr>
                <tr>
                  <td>Pay Later</td>
                  <td className="text-gray-9 fw-medium text-end">$3355.84</td>
                </tr>
                <tr>
                  <td>Total Payment</td>
                  <td className="text-gray-9 fw-medium text-end">$565597.88</td>
                </tr>
                <tr>
                  <td>Total Sale Return</td>
                  <td className="text-gray-9 fw-medium text-end">$565597.88</td>
                </tr>
                <tr>
                  <td>Total Expense:</td>
                  <td className="text-gray-9 fw-medium text-end">$565597.88</td>
                </tr>
                <tr>
                  <td className="text-gray-9 fw-bold bg-secondary-transparent">
                    Total Cash
                  </td>
                  <td className="text-gray-9 fw-bold text-end bg-secondary-transparent">
                    $587130.97
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div className="modal-footer d-flex justify-content-end gap-2 flex-wrap">
          <button
            type="button"
            className="btn btn-md btn-primary"
            data-bs-dismiss="modal"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
  {/* /Today&apos;s Sale */}
  {/* Today&apos;s Profit */}
  <div
    className="modal fade pos-modal"
    id="today-profit"
    tabIndex={-1}
    aria-hidden="true"
  >
    <div
      className="modal-dialog modal-md modal-dialog-centered"
      role="document"
    >
      <div className="modal-content">
        <div className="modal-header">
          <h5 className="modal-title">Today&apos;s Profit</h5>
          <button
            type="button"
            className="close"
            data-bs-dismiss="modal"
            aria-label="Close"
          >
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <div className="modal-body">
          <div className="row justify-content-center g-3 mb-3">
            <div className="col-lg-4 col-md-6 d-flex">
              <div className="border border-success bg-success-transparent br-8 p-3 flex-fill">
                <p className="fs-16 text-gray-9 mb-1">Total Sale</p>
                <h3 className="text-success">$89954</h3>
              </div>
            </div>
            <div className="col-lg-4 col-md-6 d-flex">
              <div className="border border-danger bg-danger-transparent br-8 p-3 flex-fill">
                <p className="fs-16 text-gray-9 mb-1">Expense</p>
                <h3 className="text-danger">$89954</h3>
              </div>
            </div>
            <div className="col-lg-4 col-md-6 d-flex">
              <div className="border border-info bg-info-transparent br-8 p-3 flex-fill">
                <p className="fs-16 text-gray-9 mb-1">Total Profit </p>
                <h3 className="text-info">$2145</h3>
              </div>
            </div>
          </div>
          <div className="table-responsive">
            <table className="table table-striped border">
              <tbody>
                <tr>
                  <td>Product Revenue</td>
                  <td className="text-gray-9 fw-medium text-end">$565597.88</td>
                </tr>
                <tr>
                  <td>Product Cost</td>
                  <td className="text-gray-9 fw-medium text-end">$3355.84</td>
                </tr>
                <tr>
                  <td>Expense</td>
                  <td className="text-gray-9 fw-medium text-end">$1959</td>
                </tr>
                <tr>
                  <td>Total Stock Adjustment</td>
                  <td className="text-gray-9 fw-medium text-end">$0</td>
                </tr>
                <tr>
                  <td>Deposit Payment</td>
                  <td className="text-gray-9 fw-medium text-end">$565597.88</td>
                </tr>
                <tr>
                  <td>Total Purchase Shipping Cost</td>
                  <td className="text-gray-9 fw-medium text-end">$3355.84</td>
                </tr>
                <tr>
                  <td>Total Sell Discount</td>
                  <td className="text-gray-9 fw-medium text-end">$565597.88</td>
                </tr>
                <tr>
                  <td>Total Sell Return</td>
                  <td className="text-gray-9 fw-medium text-end">$3355.84</td>
                </tr>
                <tr>
                  <td>Closing Stock</td>
                  <td className="text-gray-9 fw-medium text-end">$3355.84</td>
                </tr>
                <tr>
                  <td>Total Sales</td>
                  <td className="text-gray-9 fw-medium text-end">$565597.88</td>
                </tr>
                <tr>
                  <td>Total Sale Return</td>
                  <td className="text-gray-9 fw-medium text-end">$565597.88</td>
                </tr>
                <tr>
                  <td>Total Expense</td>
                  <td className="text-gray-9 fw-medium text-end">$565597.88</td>
                </tr>
                <tr>
                  <td className="text-gray-9 fw-bold bg-secondary-transparent">
                    Total Cash
                  </td>
                  <td className="text-gray-9 fw-bold text-end bg-secondary-transparent">
                    $587130.97
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div className="modal-footer d-flex justify-content-end gap-2 flex-wrap">
          <button
            type="button"
            className="btn btn-md btn-primary"
            data-bs-dismiss="modal"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
  {/* /Today&apos;s Profit */}

  {/* Save Order Modal */}
  <div
    className="modal fade pos-modal"
    id="save-order"
    tabIndex={-1}
    aria-hidden="true"
  >
    <div
      className="modal-dialog modal-md modal-dialog-centered"
      role="document"
    >
      <div className="modal-content">
        <div className="modal-header">
          <h5 className="modal-title">Save Order</h5>
          <button
            type="button"
            className="close"
            data-bs-dismiss="modal"
            aria-label="Close"
          >
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <form onSubmit={(e) => { e.preventDefault(); handleSaveOrderForCustomer(); }}>
          <div className="modal-body pb-1">
            <div className="row">
              <div className="col-12">
                <div className="mb-3">
                  <label className="form-label">Order Notes (Optional)</label>
                  <textarea
                    className="form-control"
                    rows="3"
                    value={orderNotes}
                    onChange={(e) => setOrderNotes(e.target.value)}
                    placeholder="Add any notes for this order..."
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="modal-footer d-flex justify-content-end gap-2 flex-wrap">
            <button
              type="button"
              className="btn btn-md btn-secondary"
              data-bs-dismiss="modal"
              disabled={isSavingOrder}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn-md btn-primary"
              disabled={isSavingOrder}
            >
              {isSavingOrder ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                  Saving...
                </>
              ) : (
                'Save Order'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
  {/* /Save Order Modal */}

  {/* Customer Orders Modal */}
  <div
    className="modal fade pos-modal"
    id="customer-orders"
    tabIndex={-1}
    aria-hidden="true"
  >
    <div
      className="modal-dialog modal-lg modal-dialog-centered"
      role="document"
    >
      <div className="modal-content">
        <div className="modal-header">
          <h5 className="modal-title">
            {selectedCustomerForOrders ? `Orders for ${selectedCustomerForOrders.name}` : 'Customer Orders'}
          </h5>
          <button
            type="button"
            className="close"
            data-bs-dismiss="modal"
            aria-label="Close"
          >
            <span aria-hidden="true">×</span>
          </button>
        </div>
        <div className="modal-body">
          {customerOrders.length === 0 ? (
            <div className="text-center py-4">
              <i className="ti ti-shopping-cart fs-48 text-muted mb-3"></i>
              <h6 className="text-muted">No saved orders found</h6>
              <p className="text-muted">This customer doesn&apos;t have any saved orders yet.</p>
            </div>
          ) : (
            <div className="table-responsive">
              <table className="table table-striped">
                <thead>
                  <tr>
                    <th>Reference</th>
                    <th>Items</th>
                    <th>Total</th>
                    <th>Status</th>
                    <th>Date</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {customerOrders.map((order) => (
                    <tr key={order.id}>
                      <td>
                        <span className="fw-medium">{order.reference}</span>
                        {order.notes && (
                          <div className="text-muted fs-12">{order.notes}</div>
                        )}
                      </td>
                      <td>{order.orderItems?.length || 0} items</td>
                      <td className="fw-medium">
                        {formatCurrency(order.orderTotal?.total || 0)}
                      </td>
                      <td>
                        <span className={`badge ${order.status === 'completed' ? 'bg-success' : 'bg-warning'}`}>
                          {order.status}
                        </span>
                      </td>
                      <td>{new Date(order.createdAt).toLocaleDateString()}</td>
                      <td>
                        <div className="d-flex gap-1">
                          <button
                            type="button"
                            className="btn btn-sm btn-primary"
                            onClick={() => handleLoadSavedOrder(order.id)}
                            title="Load Order"
                          >
                            <i className="ti ti-download"></i>
                          </button>
                          <button
                            type="button"
                            className="btn btn-sm btn-danger"
                            onClick={() => handleDeleteSavedOrder(order.id, order.reference)}
                            title="Delete Order"
                          >
                            <i className="ti ti-trash"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
        <div className="modal-footer d-flex justify-content-end gap-2 flex-wrap">
          <button
            type="button"
            className="btn btn-md btn-secondary"
            data-bs-dismiss="modal"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
  {/* /Customer Orders Modal */}

  {/* Tax Categories Settings Modal */}
  <TaxCategoriesModal />
</>

  )
}

export default PosModals