/**
 * Malaysian Tax Calculation Utilities
 * Handles tax inclusive, exclusive, and exempt calculations based on Malaysian tax standards
 */

import CompanyService from '../services/company.service';

/**
 * Truncate number to 5 decimal places (Malaysian tax calculation standard)
 * @param {number} value - Value to truncate
 * @returns {number} Truncated value
 */
export const truncateToFiveDecimalPlaces = (value) => {
  return Math.floor(value * 100000) / 100000;
};

/**
 * Round number to specified decimal places using "away from zero" method
 * Mimics C# Math.Round with MidpointRounding.AwayFromZero
 * @param {number} value - Value to round
 * @param {number} decimals - Number of decimal places
 * @returns {number} Rounded value
 */
export const roundAwayFromZero = (value, decimals = 2) => {
  const factor = Math.pow(10, decimals);
  const scaled = value * factor;

  // Handle midpoint rounding away from zero
  if (scaled >= 0) {
    return Math.floor(scaled + 0.5) / factor;
  } else {
    return Math.ceil(scaled - 0.5) / factor;
  }
};

/**
 * Calculate Malaysian rounding adjustment (to nearest 5 sen)
 * Mimics C# CalculateRoundingAdjustment method
 * @param {number} subTotalAmount - Subtotal amount before rounding
 * @returns {Object} Object containing grandTotalAmount and grandTotalAdjustmentAmount
 */
export const calculateRoundingAdjustment = (subTotalAmount) => {
  // C# equivalent: Math.Round(subTotalAmount * 20, MidpointRounding.AwayFromZero) / 20
  const grandTotalAmount = roundAwayFromZero(subTotalAmount * 20, 0) / 20;
  const grandTotalAdjustmentAmount = subTotalAmount - grandTotalAmount;

  return {
    grandTotalAmount: parseFloat(grandTotalAmount.toFixed(2)),
    grandTotalAdjustmentAmount: parseFloat(grandTotalAdjustmentAmount.toFixed(2))
  };
};

/**
 * Core Malaysian tax calculation function (shared utility)
 * @param {number} unitAmount - Unit price
 * @param {number} quantity - Quantity
 * @param {number|null} salesTaxRate - Sales tax rate percentage
 * @param {number|null} serviceTaxRate - Service tax rate percentage
 * @param {boolean} isTaxExcl - True for tax exclusive, false for tax inclusive
 * @param {boolean} isTaxExempt - True if item is tax exempt
 * @returns {Object} Tax calculation results
 */
export const calculateMalaysianTax = (unitAmount, quantity, salesTaxRate, serviceTaxRate, isTaxExcl = true, isTaxExempt = false) => {
  // Truncate inputs to 5 decimal places
  unitAmount = truncateToFiveDecimalPlaces(unitAmount);
  quantity = truncateToFiveDecimalPlaces(quantity);

  let salesTaxAmount = 0.00;
  let serviceTaxAmount = 0.00;
  let taxExclAmount = 0.00;
  let taxInclAmount = 0.00;
  let totalUnitAmountWOTax = 0.00;
  let subTotalAmount = 0.00;
  let taxAmount = 0.00;

  // Calculate total amount before tax calculation
  // Use C#-compatible rounding: Math.Round(unitAmount * quantity, 2, MidpointRounding.AwayFromZero)
  const totalUnitAmount = roundAwayFromZero(unitAmount * quantity, 2);

  // If tax exempt, return amounts without tax
  if (isTaxExempt) {
    return {
      salesTaxAmount: 0.00,
      serviceTaxAmount: 0.00,
      taxExclAmount: 0.00,
      taxInclAmount: 0.00,
      totalUnitAmountWOTax: totalUnitAmount,
      totalUnitAmount: totalUnitAmount,
      subTotalAmount: totalUnitAmount,
      taxAmount: 0.00
    };
  }

  // Calculate tax amount based on Malaysian logic
  if (isTaxExcl) {
    // Tax Exclusive: Add tax on top of the amount
    // C# equivalent: Math.Round(totalUnitAmount * (salesTaxRate / 100), 2, MidpointRounding.AwayFromZero)
    salesTaxAmount = salesTaxRate != null ?
      roundAwayFromZero(totalUnitAmount * (salesTaxRate / 100), 2) : 0.00;
    serviceTaxAmount = serviceTaxRate != null ?
      roundAwayFromZero(totalUnitAmount * (serviceTaxRate / 100), 2) : 0.00;

    taxAmount = salesTaxAmount + serviceTaxAmount;
    taxExclAmount = taxAmount;
    totalUnitAmountWOTax = totalUnitAmount;
    subTotalAmount = totalUnitAmount + taxAmount;
  } else {
    // Tax Inclusive: Extract tax from the amount
    // C# equivalent: totalUnitAmount - Math.Round(totalUnitAmount / (1 + (salesTaxRate / 100)), 2, MidpointRounding.AwayFromZero)
    salesTaxAmount = salesTaxRate != null ?
      totalUnitAmount - roundAwayFromZero(totalUnitAmount / (1 + (salesTaxRate / 100)), 2) : 0.00;
    serviceTaxAmount = serviceTaxRate != null ?
      totalUnitAmount - roundAwayFromZero(totalUnitAmount / (1 + (serviceTaxRate / 100)), 2) : 0.00;

    taxAmount = salesTaxAmount + serviceTaxAmount;
    taxInclAmount = taxAmount;
    totalUnitAmountWOTax = totalUnitAmount - taxAmount;
    subTotalAmount = totalUnitAmount;
  }

  return {
    salesTaxAmount: parseFloat(salesTaxAmount.toFixed(2)),
    serviceTaxAmount: parseFloat(serviceTaxAmount.toFixed(2)),
    taxExclAmount: parseFloat(taxExclAmount.toFixed(2)),
    taxInclAmount: parseFloat(taxInclAmount.toFixed(2)),
    totalUnitAmountWOTax: parseFloat(totalUnitAmountWOTax.toFixed(2)),
    totalUnitAmount: parseFloat(totalUnitAmount.toFixed(2)),
    subTotalAmount: parseFloat(subTotalAmount.toFixed(2)),
    taxAmount: parseFloat(taxAmount.toFixed(2))
  };
};

/**
 * Get company tax configuration from API
 * @returns {Promise<Object>} Company tax configuration
 */
export const getCompanyTaxConfig = async () => {
  try {
    const companyProfile = await CompanyService.getCompanyProfile();
    
    return {
      defaultSalesTaxNo: companyProfile.defaultSalesTaxNo || null,
      defaultServiceTaxNo: companyProfile.defaultServiceTaxNo || null,
      defaultSalesTaxNoId: companyProfile.defaultSalesTaxNoId || null,
      defaultServiceTaxNoId: companyProfile.defaultServiceTaxNoId || null,
      serviceCharge: companyProfile.serviceCharge || 0,
      taxCategory: companyProfile.taxCategory || null,
      isServiceTaxCompany: companyProfile.isServiceTaxCompany || false
    };
  } catch (error) {
    console.error('Error fetching company tax config:', error);
    return {
      defaultSalesTaxNo: null,
      defaultServiceTaxNo: null,
      defaultSalesTaxNoId: null,
      defaultServiceTaxNoId: null,
      serviceCharge: 0,
      taxCategory: null,
      isServiceTaxCompany: false
    };
  }
};

/**
 * Determine tax rates for a product based on company settings
 * @param {Object} product - Product object
 * @param {Object} companyTaxConfig - Company tax configuration
 * @returns {Promise<Object>} Tax rates for the product
 */
export const getProductTaxRates = async (product, companyTaxConfig = null) => {
  try {
    if (!companyTaxConfig) {
      companyTaxConfig = await getCompanyTaxConfig();
    }

    let salesTaxRate = null;
    let serviceTaxRate = null;

    // If product is tax exempt, return zero rates
    if (product.isTaxExempt) {
      return { salesTaxRate: null, serviceTaxRate: null };
    }

    // Check for custom tax rates on product
    if (product.customSalesTaxNoId && companyTaxConfig.defaultSalesTaxNo) {
      // Use custom sales tax rate if specified
      salesTaxRate = companyTaxConfig.defaultSalesTaxNo.chargePercentage || 0;
    } else if (companyTaxConfig.defaultSalesTaxNo) {
      // Use company default sales tax rate
      salesTaxRate = companyTaxConfig.defaultSalesTaxNo.chargePercentage || 0;
    }

    if (product.customServiceTaxNoId && companyTaxConfig.defaultServiceTaxNo) {
      // Use custom service tax rate if specified
      serviceTaxRate = companyTaxConfig.defaultServiceTaxNo.chargePercentage || 0;
    } else if (companyTaxConfig.defaultServiceTaxNo && !salesTaxRate) {
      // Use company default service tax rate if no sales tax
      serviceTaxRate = companyTaxConfig.defaultServiceTaxNo.chargePercentage || 0;
    }

    return { salesTaxRate, serviceTaxRate };
  } catch (error) {
    console.error('Error determining product tax rates:', error);
    return { salesTaxRate: null, serviceTaxRate: null };
  }
};

/**
 * Calculate Malaysian tax for a single item (enhanced version with company integration)
 * @param {Object} item - Order item object
 * @param {Object} companyTaxConfig - Company tax configuration
 * @returns {Promise<Object>} Tax calculation results
 */
export const calculateItemMalaysianTax = async (item, companyTaxConfig = null) => {
  try {
    if (!companyTaxConfig) {
      companyTaxConfig = await getCompanyTaxConfig();
    }

    const unitAmount = parseFloat(item.price) || 0;
    const quantity = parseFloat(item.quantity) || 0;

    // Get tax rates for this item
    const { salesTaxRate, serviceTaxRate } = await getProductTaxRates(item, companyTaxConfig);

    // Use item-level tax settings or defaults
    const isTaxExempt = item.isTaxExempt || false;
    const isTaxExcl = item.isTaxExcl !== undefined ? item.isTaxExcl : true;

    // Use the shared Malaysian tax calculation function
    return calculateMalaysianTax(unitAmount, quantity, salesTaxRate, serviceTaxRate, isTaxExcl, isTaxExempt);
  } catch (error) {
    console.error('Error calculating item Malaysian tax:', error);
    // Return safe defaults using the shared function
    const unitAmount = parseFloat(item.price) || 0;
    const quantity = parseFloat(item.quantity) || 0;

    return calculateMalaysianTax(unitAmount, quantity, null, null, true, true); // Tax exempt fallback
  }
};

/**
 * Calculate service charges based on company profile
 * @param {number} subtotal - Subtotal amount
 * @param {Object} companyTaxConfig - Company tax configuration
 * @returns {number} Service charges amount
 */
export const calculateServiceCharges = (subtotal, companyTaxConfig) => {
  try {
    const serviceChargePercentage = companyTaxConfig?.serviceCharge || 0;
    const serviceCharges = (subtotal * serviceChargePercentage) / 100;
    return parseFloat(serviceCharges.toFixed(2));
  } catch (error) {
    console.error('Error calculating service charges:', error);
    return 0.00;
  }
};

/**
 * Get POS feature settings from local storage
 * @returns {Object} POS feature settings
 */
export const getPOSFeatureSettings = () => {
  try {
    const settings = localStorage.getItem('pos_feature_settings');
    return settings ? JSON.parse(settings) : {
      enableVoucher: false,        // Voucher feature DISABLED by default (disabled feature)
      enableServiceCharge: true,   // Service charge feature enabled/disabled
      enableRounding: true,        // Rounding feature enabled/disabled
      enableDiscount: true,        // Discount feature enabled/disabled
      enableTax: true             // Tax feature enabled/disabled
    };
  } catch (error) {
    console.error('Error retrieving POS feature settings:', error);
    return {
      enableVoucher: false,        // Default: DISABLED (disabled feature)
      enableServiceCharge: true,
      enableRounding: true,
      enableDiscount: true,
      enableTax: true
    };
  }
};

/**
 * Save POS feature settings to local storage
 * @param {Object} settings - POS feature settings
 */
export const savePOSFeatureSettings = (settings) => {
  try {
    localStorage.setItem('pos_feature_settings', JSON.stringify(settings));
  } catch (error) {
    console.error('Error saving POS feature settings:', error);
  }
};

/**
 * Check if voucher feature is enabled
 * @returns {boolean} True if voucher is enabled
 */
export const isVoucherEnabled = () => {
  const settings = getPOSFeatureSettings();
  return settings.enableVoucher;
};

/**
 * Toggle voucher feature on/off
 * @param {boolean} enabled - Enable or disable voucher
 */
export const setVoucherEnabled = (enabled) => {
  const settings = getPOSFeatureSettings();
  settings.enableVoucher = enabled;
  savePOSFeatureSettings(settings);
};

/**
 * Check if rounding feature is enabled
 * @returns {boolean} True if rounding is enabled
 */
export const isRoundingEnabled = () => {
  const settings = getPOSFeatureSettings();
  return settings.enableRounding;
};

/**
 * Toggle rounding feature on/off
 * @param {boolean} enabled - Enable or disable rounding
 */
export const setRoundingEnabled = (enabled) => {
  const settings = getPOSFeatureSettings();
  settings.enableRounding = enabled;
  savePOSFeatureSettings(settings);

  // Also update POS payment summary settings
  try {
    const paymentSummary = JSON.parse(localStorage.getItem('pos_payment_summary') || '{}');
    paymentSummary.roundOff = enabled;
    localStorage.setItem('pos_payment_summary', JSON.stringify(paymentSummary));
  } catch (error) {
    console.error('Error updating POS payment summary rounding:', error);
  }
};

/**
 * Format tax breakdown for display
 * @param {Object} taxCalculation - Tax calculation result
 * @returns {Object} Formatted tax breakdown
 */
export const formatTaxBreakdown = (taxCalculation) => {
  return {
    subtotal: taxCalculation.subtotal || 0,
    amountWOTax: taxCalculation.amountWOTax || 0,
    salesTax: taxCalculation.salesTax || 0,
    serviceTax: taxCalculation.serviceTax || 0,
    totalTax: taxCalculation.tax || 0,
    taxInclusive: taxCalculation.taxInclAmount || 0,
    taxExclusive: taxCalculation.taxExclAmount || 0,
    serviceCharges: taxCalculation.serviceCharges || 0,
    discount: taxCalculation.discount || 0,
    voucher: taxCalculation.voucher || 0,
    roundOff: taxCalculation.roundOffAmount || 0,
    total: taxCalculation.total || 0
  };
};

export default {
  getCompanyTaxConfig,
  getProductTaxRates,
  calculateMalaysianTax,
  calculateItemMalaysianTax,
  calculateServiceCharges,
  formatTaxBreakdown,
  truncateToFiveDecimalPlaces,
  roundAwayFromZero,
  calculateRoundingAdjustment,
  getPOSFeatureSettings,
  savePOSFeatureSettings,
  isVoucherEnabled,
  setVoucherEnabled,
  isRoundingEnabled,
  setRoundingEnabled
};
