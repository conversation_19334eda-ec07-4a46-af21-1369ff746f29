/**
 * Malaysian Tax Calculation Utilities
 * Handles tax inclusive, exclusive, and exempt calculations based on Malaysian tax standards
 */

import CompanyService from '../services/company.service';

/**
 * Get company tax configuration from API
 * @returns {Promise<Object>} Company tax configuration
 */
export const getCompanyTaxConfig = async () => {
  try {
    const companyProfile = await CompanyService.getCompanyProfile();
    
    return {
      defaultSalesTaxNo: companyProfile.defaultSalesTaxNo || null,
      defaultServiceTaxNo: companyProfile.defaultServiceTaxNo || null,
      defaultSalesTaxNoId: companyProfile.defaultSalesTaxNoId || null,
      defaultServiceTaxNoId: companyProfile.defaultServiceTaxNoId || null,
      serviceCharge: companyProfile.serviceCharge || 0,
      taxCategory: companyProfile.taxCategory || null,
      isServiceTaxCompany: companyProfile.isServiceTaxCompany || false
    };
  } catch (error) {
    console.error('Error fetching company tax config:', error);
    return {
      defaultSalesTaxNo: null,
      defaultServiceTaxNo: null,
      defaultSalesTaxNoId: null,
      defaultServiceTaxNoId: null,
      serviceCharge: 0,
      taxCategory: null,
      isServiceTaxCompany: false
    };
  }
};

/**
 * Determine tax rates for a product based on company settings
 * @param {Object} product - Product object
 * @param {Object} companyTaxConfig - Company tax configuration
 * @returns {Promise<Object>} Tax rates for the product
 */
export const getProductTaxRates = async (product, companyTaxConfig = null) => {
  try {
    if (!companyTaxConfig) {
      companyTaxConfig = await getCompanyTaxConfig();
    }

    let salesTaxRate = null;
    let serviceTaxRate = null;

    // If product is tax exempt, return zero rates
    if (product.isTaxExempt) {
      return { salesTaxRate: null, serviceTaxRate: null };
    }

    // Check for custom tax rates on product
    if (product.customSalesTaxNoId && companyTaxConfig.defaultSalesTaxNo) {
      // Use custom sales tax rate if specified
      salesTaxRate = companyTaxConfig.defaultSalesTaxNo.chargePercentage || 0;
    } else if (companyTaxConfig.defaultSalesTaxNo) {
      // Use company default sales tax rate
      salesTaxRate = companyTaxConfig.defaultSalesTaxNo.chargePercentage || 0;
    }

    if (product.customServiceTaxNoId && companyTaxConfig.defaultServiceTaxNo) {
      // Use custom service tax rate if specified
      serviceTaxRate = companyTaxConfig.defaultServiceTaxNo.chargePercentage || 0;
    } else if (companyTaxConfig.defaultServiceTaxNo && !salesTaxRate) {
      // Use company default service tax rate if no sales tax
      serviceTaxRate = companyTaxConfig.defaultServiceTaxNo.chargePercentage || 0;
    }

    return { salesTaxRate, serviceTaxRate };
  } catch (error) {
    console.error('Error determining product tax rates:', error);
    return { salesTaxRate: null, serviceTaxRate: null };
  }
};

/**
 * Calculate Malaysian tax for a single item (enhanced version)
 * @param {Object} item - Order item object
 * @param {Object} companyTaxConfig - Company tax configuration
 * @returns {Promise<Object>} Tax calculation results
 */
export const calculateItemMalaysianTax = async (item, companyTaxConfig = null) => {
  try {
    if (!companyTaxConfig) {
      companyTaxConfig = await getCompanyTaxConfig();
    }

    const unitAmount = parseFloat(item.price) || 0;
    const quantity = parseFloat(item.quantity) || 0;
    
    // Get tax rates for this item
    const { salesTaxRate, serviceTaxRate } = await getProductTaxRates(item, companyTaxConfig);
    
    // Use item-level tax settings or defaults
    const isTaxExempt = item.isTaxExempt || false;
    const isTaxExcl = item.isTaxExcl !== undefined ? item.isTaxExcl : true;

    // Truncate inputs to 5 decimal places (Malaysian standard)
    const truncatedUnitAmount = Math.floor(unitAmount * 100000) / 100000;
    const truncatedQuantity = Math.floor(quantity * 100000) / 100000;
    
    let salesTaxAmount = 0.00;
    let serviceTaxAmount = 0.00;
    let taxExclAmount = 0.00;
    let taxInclAmount = 0.00;
    let totalUnitAmountWOTax = 0.00;
    let subTotalAmount = 0.00;
    let taxAmount = 0.00;

    // Calculate total amount before tax calculation
    const totalUnitAmount = Math.round(truncatedUnitAmount * truncatedQuantity * 100) / 100;

    // If tax exempt, return amounts without tax
    if (isTaxExempt) {
      return {
        salesTaxAmount: 0.00,
        serviceTaxAmount: 0.00,
        taxExclAmount: 0.00,
        taxInclAmount: 0.00,
        totalUnitAmountWOTax: totalUnitAmount,
        totalUnitAmount: totalUnitAmount,
        subTotalAmount: totalUnitAmount,
        taxAmount: 0.00
      };
    }

    // Calculate tax amount based on Malaysian logic
    if (isTaxExcl) {
      // Tax Exclusive: Add tax on top of the amount
      salesTaxAmount = salesTaxRate != null ? 
        Math.round(totalUnitAmount * (salesTaxRate / 100) * 100) / 100 : 0.00;
      serviceTaxAmount = serviceTaxRate != null ? 
        Math.round(totalUnitAmount * (serviceTaxRate / 100) * 100) / 100 : 0.00;
      
      taxAmount = salesTaxAmount + serviceTaxAmount;
      taxExclAmount = taxAmount;
      totalUnitAmountWOTax = totalUnitAmount;
      subTotalAmount = totalUnitAmount + taxAmount;
    } else {
      // Tax Inclusive: Extract tax from the amount
      salesTaxAmount = salesTaxRate != null ? 
        totalUnitAmount - Math.round(totalUnitAmount / (1 + (salesTaxRate / 100)) * 100) / 100 : 0.00;
      serviceTaxAmount = serviceTaxRate != null ? 
        totalUnitAmount - Math.round(totalUnitAmount / (1 + (serviceTaxRate / 100)) * 100) / 100 : 0.00;
      
      taxAmount = salesTaxAmount + serviceTaxAmount;
      taxInclAmount = taxAmount;
      totalUnitAmountWOTax = totalUnitAmount - taxAmount;
      subTotalAmount = totalUnitAmount;
    }

    return {
      salesTaxAmount: parseFloat(salesTaxAmount.toFixed(2)),
      serviceTaxAmount: parseFloat(serviceTaxAmount.toFixed(2)),
      taxExclAmount: parseFloat(taxExclAmount.toFixed(2)),
      taxInclAmount: parseFloat(taxInclAmount.toFixed(2)),
      totalUnitAmountWOTax: parseFloat(totalUnitAmountWOTax.toFixed(2)),
      totalUnitAmount: parseFloat(totalUnitAmount.toFixed(2)),
      subTotalAmount: parseFloat(subTotalAmount.toFixed(2)),
      taxAmount: parseFloat(taxAmount.toFixed(2))
    };
  } catch (error) {
    console.error('Error calculating item Malaysian tax:', error);
    // Return safe defaults
    const unitAmount = parseFloat(item.price) || 0;
    const quantity = parseFloat(item.quantity) || 0;
    const totalAmount = unitAmount * quantity;
    
    return {
      salesTaxAmount: 0.00,
      serviceTaxAmount: 0.00,
      taxExclAmount: 0.00,
      taxInclAmount: 0.00,
      totalUnitAmountWOTax: totalAmount,
      totalUnitAmount: totalAmount,
      subTotalAmount: totalAmount,
      taxAmount: 0.00
    };
  }
};

/**
 * Calculate service charges based on company profile
 * @param {number} subtotal - Subtotal amount
 * @param {Object} companyTaxConfig - Company tax configuration
 * @returns {number} Service charges amount
 */
export const calculateServiceCharges = (subtotal, companyTaxConfig) => {
  try {
    const serviceChargePercentage = companyTaxConfig?.serviceCharge || 0;
    const serviceCharges = (subtotal * serviceChargePercentage) / 100;
    return parseFloat(serviceCharges.toFixed(2));
  } catch (error) {
    console.error('Error calculating service charges:', error);
    return 0.00;
  }
};

/**
 * Format tax breakdown for display
 * @param {Object} taxCalculation - Tax calculation result
 * @returns {Object} Formatted tax breakdown
 */
export const formatTaxBreakdown = (taxCalculation) => {
  return {
    subtotal: taxCalculation.subtotal || 0,
    amountWOTax: taxCalculation.amountWOTax || 0,
    salesTax: taxCalculation.salesTax || 0,
    serviceTax: taxCalculation.serviceTax || 0,
    totalTax: taxCalculation.tax || 0,
    taxInclusive: taxCalculation.taxInclAmount || 0,
    taxExclusive: taxCalculation.taxExclAmount || 0,
    serviceCharges: taxCalculation.serviceCharges || 0,
    discount: taxCalculation.discount || 0,
    voucher: taxCalculation.voucher || 0,
    roundOff: taxCalculation.roundOffAmount || 0,
    total: taxCalculation.total || 0
  };
};

export default {
  getCompanyTaxConfig,
  getProductTaxRates,
  calculateItemMalaysianTax,
  calculateServiceCharges,
  formatTaxBreakdown
};
