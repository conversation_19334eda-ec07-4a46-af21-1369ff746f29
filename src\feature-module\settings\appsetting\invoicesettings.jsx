
import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import Select from 'react-select'
import SettingsSideBar from '../settingssidebar'
import ImageWithBasePath from '../../../core/img/imagewithbasebath'
import RefreshIcon from '../../../core/common/tooltip-content/refresh'
import CollapesIcon from '../../../core/common/tooltip-content/collapes'
import {
  isVoucherEnabled,
  isRoundingEnabled,
  setRoundingEnabled
} from '../../../core/utils/malaysianTax'

const InvoiceSettings = () => {
    // State for voucher setting (read-only)
    const [voucherEnabled, setVoucherEnabledState] = useState(false);
    // State for rounding setting (linked with POS)
    const [roundingEnabled, setRoundingEnabledState] = useState(true);

    const listofnumbers = [
        { value: '5', label: '5' },
        { value: '6', label: '6' },
        { value: '7', label: '7' },
    ];
    const roundoff = [
        { value: 'Round Off Up', label: 'Round Off Up' },
    ];

    // Load settings on component mount
    useEffect(() => {
        const loadSettings = () => {
            try {
                const voucherEnabled = isVoucherEnabled();
                const roundingEnabled = isRoundingEnabled();
                setVoucherEnabledState(voucherEnabled);
                setRoundingEnabledState(roundingEnabled);
            } catch (error) {
                console.error('Error loading settings:', error);
            }
        };

        loadSettings();
    }, []);

    // Handle rounding toggle (linked with POS)
    const handleRoundingToggle = (enabled) => {
        try {
            setRoundingEnabledState(enabled);
            setRoundingEnabled(enabled);

            // Show success message
            console.log(`Rounding ${enabled ? 'enabled' : 'disabled'} successfully`);

            // Optional: Show user feedback
            alert(`Malaysian rounding has been ${enabled ? 'enabled' : 'disabled'}. This affects both Invoice and POS calculations.`);
        } catch (error) {
            console.error('Error updating rounding setting:', error);
            // Revert the change on error
            setRoundingEnabledState(!enabled);
            alert('Failed to update rounding setting. Please try again.');
        }
    };

    // Note: Voucher toggle is disabled/read-only - no handler needed


    return (
        <div>
            <div className="page-wrapper">
                <div className="content settings-content">
                    <div className="page-header settings-pg-header">
                        <div className="add-item d-flex">
                            <div className="page-title">
                                <h4>Settings</h4>
                                <h6>Manage your settings on portal</h6>
                            </div>
                        </div>
                        <ul className="table-top-head">
                            <RefreshIcon />
                            <CollapesIcon />
                        </ul>
                    </div>
                    <div className="row">
                        <div className="col-xl-12">
                            <div className="settings-wrapper d-flex">
                                <SettingsSideBar />
                                <div className="card flex-fill mb-0">
                                    <form action="invoice-settings.html">
                                        <div className="card-header">
                                            <h4>Invoice Settings</h4>
                                        </div>
                                        <div className="card-body ">
                                            <ul className="logo-company">
                                                <li>
                                                    <div className="row">
                                                        <div className="col-md-4">
                                                            <div className="logo-info me-0 mb-3 mb-md-0">
                                                                <h6>Invoice Logo</h6>
                                                                <p>Upload Logo of your Company to display in Invoice</p>
                                                            </div>
                                                        </div>
                                                        <div className="col-md-6">
                                                            <div className="profile-pic-upload mb-0 me-0">
                                                                <div className="new-employee-field">
                                                                    <div className="mb-3 mb-md-0">
                                                                        <div className="image-upload mb-0">
                                                                            <input type="file" />
                                                                            <div className="image-uploads">
                                                                                <h4>
                                                                                    <i data-feather="upload" />
                                                                                    Upload Photo
                                                                                </h4>
                                                                            </div>
                                                                        </div>
                                                                        <span>
                                                                            For better preview recommended size is 450px x 450px. Max
                                                                            size 5mb.
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-md-2">
                                                            <div className="new-logo ms-auto">
                                                                <Link to="#">
                                                                    <ImageWithBasePath src="assets/img/logo-small.png" alt="Logo" />
                                                                </Link>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                            <div className="localization-info">
                                                <div className="row align-items-center">
                                                    <div className="col-sm-4">
                                                        <div className="setting-info">
                                                            <h6>Invoice Prefix</h6>
                                                            <p>Add prefix to your invoice</p>
                                                        </div>
                                                    </div>
                                                    <div className="col-sm-4">
                                                        <div className="localization-select">
                                                            <input
                                                                type="text"
                                                                className="form-control"
                                                                defaultValue="INV -"
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row align-items-center">
                                                    <div className="col-sm-4">
                                                        <div className="setting-info">
                                                            <h6>Invoice Due</h6>
                                                            <p>Select due date to display in Invoice</p>
                                                        </div>
                                                    </div>
                                                    <div className="col-sm-4">
                                                        <div className="localization-select d-flex align-items-center fixed-width">
                                                            <Select
                                                                classNamePrefix="react-select"
                                                                options={listofnumbers}
                                                                placeholder="Choose"
                                                            />
                                                            <span className="ms-2">Days</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row align-items-center">
                                                    <div className="col-sm-4">
                                                        <div className="setting-info">
                                                            <h6>Invoice Round Off</h6>
                                                            <p>Malaysian 5 sen rounding (linked with POS)</p>
                                                        </div>
                                                    </div>
                                                    <div className="col-sm-4">
                                                        <div className="localization-select d-flex align-items-center width-custom">
                                                            <div className="status-toggle modal-status d-flex justify-content-between align-items-center me-3">
                                                                <input
                                                                    type="checkbox"
                                                                    id="roundingToggle"
                                                                    className="check"
                                                                    checked={roundingEnabled}
                                                                    onChange={(e) => handleRoundingToggle(e.target.checked)}
                                                                />
                                                                <label htmlFor="roundingToggle" className="checktoggle" />
                                                            </div>
                                                            <Select
                                                                classNamePrefix="react-select"
                                                                options={roundoff}
                                                                placeholder="Malaysian 5 sen"
                                                                value={roundoff[0]}
                                                                isDisabled={!roundingEnabled}
                                                            />
                                                            <span className={`ms-2 badge ${roundingEnabled ? 'bg-success' : 'bg-secondary'}`}>
                                                                {roundingEnabled ? 'Enabled' : 'Disabled'}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row align-items-center">
                                                    <div className="col-sm-4">
                                                        <div className="setting-info">
                                                            <h6>Show Company Details</h6>
                                                            <p>Show / Hide Company Details in Invoice</p>
                                                        </div>
                                                    </div>
                                                    <div className="col-sm-4">
                                                        <div className="localization-select d-flex align-items-center">
                                                            <div className="status-toggle modal-status d-flex justify-content-between align-items-center me-3">
                                                                <input
                                                                    type="checkbox"
                                                                    id="user4"
                                                                    className="check"
                                                                    defaultChecked
                                                                />
                                                                <label htmlFor="user4" className="checktoggle" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                {/* Voucher Feature Setting - Disabled */}
                                                <div className="row align-items-center">
                                                    <div className="col-sm-4">
                                                        <div className="setting-info">
                                                            <h6>Enable Voucher Feature</h6>
                                                            <p>Voucher functionality in POS system</p>
                                                        </div>
                                                    </div>
                                                    <div className="col-sm-4">
                                                        <div className="localization-select d-flex align-items-center">
                                                            <div className="status-toggle modal-status d-flex justify-content-between align-items-center me-3">
                                                                <input
                                                                    type="checkbox"
                                                                    id="voucherToggle"
                                                                    className="check"
                                                                    checked={voucherEnabled}
                                                                    disabled={true}
                                                                    readOnly
                                                                    style={{ opacity: 0.6, cursor: 'not-allowed' }}
                                                                />
                                                                <label
                                                                    htmlFor="voucherToggle"
                                                                    className="checktoggle"
                                                                    style={{ opacity: 0.6, cursor: 'not-allowed' }}
                                                                />
                                                            </div>
                                                            <span className={`ms-2 badge ${voucherEnabled ? 'bg-success' : 'bg-secondary'}`}>
                                                                {voucherEnabled ? 'Enabled' : 'Disabled'}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row">
                                                    <div className="col-sm-4">
                                                        <div className="setting-info">
                                                            <h6>Invoice Header Terms</h6>
                                                        </div>
                                                    </div>
                                                    <div className="col-sm-8">
                                                        <div className="mb-3">
                                                            <textarea
                                                                rows={4}
                                                                className="form-control"
                                                                placeholder="Type your message"
                                                                defaultValue={""}
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row">
                                                    <div className="col-sm-4">
                                                        <div className="setting-info">
                                                            <h6>Invoice Footer Terms</h6>
                                                        </div>
                                                    </div>
                                                    <div className="col-sm-8">
                                                        <div className="mb-3">
                                                            <textarea
                                                                rows={4}
                                                                className="form-control"
                                                                placeholder="Type your message"
                                                                defaultValue={""}
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="d-flex align-items-center justify-content-end">
                                                <button type="button" className="btn btn-secondary me-2">
                                                    Cancel
                                                </button>
                                                <button type="submit" className="btn btn-primary">
                                                    Save Changes
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    )
}

export default InvoiceSettings
