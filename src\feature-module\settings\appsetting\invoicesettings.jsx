
import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import Select from 'react-select'
import SettingsSideBar from '../settingssidebar'
import ImageWithBasePath from '../../../core/img/imagewithbasebath'
import RefreshIcon from '../../../core/common/tooltip-content/refresh'
import CollapesIcon from '../../../core/common/tooltip-content/collapes'
import {
  getPOSFeatureSettings,
  savePOSFeatureSettings,
  setVoucherEnabled,
  isVoucherEnabled
} from '../../../core/utils/malaysianTax'

const InvoiceSettings = () => {
    // State for voucher setting
    const [voucherEnabled, setVoucherEnabledState] = useState(false);
    const [isLoading, setIsLoading] = useState(true);

    const listofnumbers = [
        { value: '5', label: '5' },
        { value: '6', label: '6' },
        { value: '7', label: '7' },
    ];
    const roundoff = [
        { value: 'Round Off Up', label: 'Round Off Up' },
    ];

    // Load voucher setting on component mount
    useEffect(() => {
        const loadVoucherSetting = () => {
            try {
                const enabled = isVoucherEnabled();
                setVoucherEnabledState(enabled);
            } catch (error) {
                console.error('Error loading voucher setting:', error);
            } finally {
                setIsLoading(false);
            }
        };

        loadVoucherSetting();
    }, []);

    // Handle voucher toggle
    const handleVoucherToggle = (enabled) => {
        try {
            setVoucherEnabledState(enabled);
            setVoucherEnabled(enabled);

            // Show success message (you can replace with toast notification)
            console.log(`Voucher ${enabled ? 'enabled' : 'disabled'} successfully`);

            // Optional: Show user feedback
            alert(`Voucher feature has been ${enabled ? 'enabled' : 'disabled'}. Please refresh the POS page to see changes.`);
        } catch (error) {
            console.error('Error updating voucher setting:', error);
            // Revert the change on error
            setVoucherEnabledState(!enabled);
            alert('Failed to update voucher setting. Please try again.');
        }
    };


    return (
        <div>
            <div className="page-wrapper">
                <div className="content settings-content">
                    <div className="page-header settings-pg-header">
                        <div className="add-item d-flex">
                            <div className="page-title">
                                <h4>Settings</h4>
                                <h6>Manage your settings on portal</h6>
                            </div>
                        </div>
                        <ul className="table-top-head">
                            <RefreshIcon />
                            <CollapesIcon />
                        </ul>
                    </div>
                    <div className="row">
                        <div className="col-xl-12">
                            <div className="settings-wrapper d-flex">
                                <SettingsSideBar />
                                <div className="card flex-fill mb-0">
                                    <form action="invoice-settings.html">
                                        <div className="card-header">
                                            <h4>Invoice Settings</h4>
                                        </div>
                                        <div className="card-body ">
                                            <ul className="logo-company">
                                                <li>
                                                    <div className="row">
                                                        <div className="col-md-4">
                                                            <div className="logo-info me-0 mb-3 mb-md-0">
                                                                <h6>Invoice Logo</h6>
                                                                <p>Upload Logo of your Company to display in Invoice</p>
                                                            </div>
                                                        </div>
                                                        <div className="col-md-6">
                                                            <div className="profile-pic-upload mb-0 me-0">
                                                                <div className="new-employee-field">
                                                                    <div className="mb-3 mb-md-0">
                                                                        <div className="image-upload mb-0">
                                                                            <input type="file" />
                                                                            <div className="image-uploads">
                                                                                <h4>
                                                                                    <i data-feather="upload" />
                                                                                    Upload Photo
                                                                                </h4>
                                                                            </div>
                                                                        </div>
                                                                        <span>
                                                                            For better preview recommended size is 450px x 450px. Max
                                                                            size 5mb.
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div className="col-md-2">
                                                            <div className="new-logo ms-auto">
                                                                <Link to="#">
                                                                    <ImageWithBasePath src="assets/img/logo-small.png" alt="Logo" />
                                                                </Link>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </li>
                                            </ul>
                                            <div className="localization-info">
                                                <div className="row align-items-center">
                                                    <div className="col-sm-4">
                                                        <div className="setting-info">
                                                            <h6>Invoice Prefix</h6>
                                                            <p>Add prefix to your invoice</p>
                                                        </div>
                                                    </div>
                                                    <div className="col-sm-4">
                                                        <div className="localization-select">
                                                            <input
                                                                type="text"
                                                                className="form-control"
                                                                defaultValue="INV -"
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row align-items-center">
                                                    <div className="col-sm-4">
                                                        <div className="setting-info">
                                                            <h6>Invoice Due</h6>
                                                            <p>Select due date to display in Invoice</p>
                                                        </div>
                                                    </div>
                                                    <div className="col-sm-4">
                                                        <div className="localization-select d-flex align-items-center fixed-width">
                                                            <Select
                                                                classNamePrefix="react-select"
                                                                options={listofnumbers}
                                                                placeholder="Choose"
                                                            />
                                                            <span className="ms-2">Days</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row align-items-center">
                                                    <div className="col-sm-4">
                                                        <div className="setting-info">
                                                            <h6>Invoice Round Off</h6>
                                                            <p>Value Roundoff in Invoice</p>
                                                        </div>
                                                    </div>
                                                    <div className="col-sm-4">
                                                        <div className="localization-select d-flex align-items-center width-custom">
                                                            <div className="status-toggle modal-status d-flex justify-content-between align-items-center me-3">
                                                                <input
                                                                    type="checkbox"
                                                                    id="user3"
                                                                    className="check"
                                                                    defaultChecked
                                                                />
                                                                <label htmlFor="user3" className="checktoggle" />
                                                            </div>
                                                            <Select
                                                                classNamePrefix="react-select"
                                                                options={roundoff}
                                                                placeholder="Choose"
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row align-items-center">
                                                    <div className="col-sm-4">
                                                        <div className="setting-info">
                                                            <h6>Show Company Details</h6>
                                                            <p>Show / Hide Company Details in Invoice</p>
                                                        </div>
                                                    </div>
                                                    <div className="col-sm-4">
                                                        <div className="localization-select d-flex align-items-center">
                                                            <div className="status-toggle modal-status d-flex justify-content-between align-items-center me-3">
                                                                <input
                                                                    type="checkbox"
                                                                    id="user4"
                                                                    className="check"
                                                                    defaultChecked
                                                                />
                                                                <label htmlFor="user4" className="checktoggle" />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                {/* Voucher Feature Setting */}
                                                <div className="row align-items-center">
                                                    <div className="col-sm-4">
                                                        <div className="setting-info">
                                                            <h6>Enable Voucher Feature</h6>
                                                            <p>Enable / Disable voucher functionality in POS system</p>
                                                        </div>
                                                    </div>
                                                    <div className="col-sm-4">
                                                        <div className="localization-select d-flex align-items-center">
                                                            <div className="status-toggle modal-status d-flex justify-content-between align-items-center me-3">
                                                                <input
                                                                    type="checkbox"
                                                                    id="voucherToggle"
                                                                    className="check"
                                                                    checked={voucherEnabled}
                                                                    onChange={(e) => handleVoucherToggle(e.target.checked)}
                                                                    disabled={isLoading}
                                                                />
                                                                <label htmlFor="voucherToggle" className="checktoggle" />
                                                            </div>
                                                            <span className={`ms-2 badge ${voucherEnabled ? 'bg-success' : 'bg-secondary'}`}>
                                                                {voucherEnabled ? 'Enabled' : 'Disabled'}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row">
                                                    <div className="col-sm-4">
                                                        <div className="setting-info">
                                                            <h6>Invoice Header Terms</h6>
                                                        </div>
                                                    </div>
                                                    <div className="col-sm-8">
                                                        <div className="mb-3">
                                                            <textarea
                                                                rows={4}
                                                                className="form-control"
                                                                placeholder="Type your message"
                                                                defaultValue={""}
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="row">
                                                    <div className="col-sm-4">
                                                        <div className="setting-info">
                                                            <h6>Invoice Footer Terms</h6>
                                                        </div>
                                                    </div>
                                                    <div className="col-sm-8">
                                                        <div className="mb-3">
                                                            <textarea
                                                                rows={4}
                                                                className="form-control"
                                                                placeholder="Type your message"
                                                                defaultValue={""}
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="d-flex align-items-center justify-content-end">
                                                <button type="button" className="btn btn-secondary me-2">
                                                    Cancel
                                                </button>
                                                <button type="submit" className="btn btn-primary">
                                                    Save Changes
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    )
}

export default InvoiceSettings
