<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voucher Toggle Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .toggle-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        .status-display {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .enabled { color: #28a745; font-weight: bold; }
        .disabled { color: #dc3545; font-weight: bold; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button.danger { background: #dc3545; }
        button.danger:hover { background: #c82333; }
        .test-result {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .test-result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎫 Voucher Feature Toggle Test</h1>
        <p>This page demonstrates the voucher enable/disable functionality for the POS system.</p>

        <div class="toggle-section">
            <h3>Current Voucher Status</h3>
            <div class="status-display">
                <strong>Voucher Feature: </strong>
                <span id="voucherStatus" class="disabled">DISABLED</span>
            </div>
            <div class="status-display">
                <strong>Last Updated: </strong>
                <span id="lastUpdated">Never</span>
            </div>
        </div>

        <div class="toggle-section">
            <h3>Toggle Controls</h3>
            <button onclick="enableVoucher()">✅ Enable Voucher</button>
            <button onclick="disableVoucher()" class="danger">❌ Disable Voucher</button>
            <button onclick="checkStatus()">🔍 Check Status</button>
            <button onclick="clearSettings()">🗑️ Clear All Settings</button>
        </div>

        <div class="toggle-section">
            <h3>Test Results</h3>
            <div id="testResults"></div>
        </div>

        <div class="toggle-section">
            <h3>All POS Feature Settings</h3>
            <div id="allSettings"></div>
        </div>

        <div class="toggle-section">
            <h3>Instructions</h3>
            <ol>
                <li><strong>Enable Voucher:</strong> Click "Enable Voucher" to turn on the voucher feature</li>
                <li><strong>Disable Voucher:</strong> Click "Disable Voucher" to turn off the voucher feature</li>
                <li><strong>Check Status:</strong> Click "Check Status" to see the current voucher setting</li>
                <li><strong>Test in POS:</strong> Go to the POS page and check if voucher row appears/disappears in payment summary</li>
                <li><strong>Clear Settings:</strong> Reset all settings to defaults</li>
            </ol>
        </div>
    </div>

    <script>
        // Malaysian Tax utility functions (simplified for testing)
        const getPOSFeatureSettings = () => {
            try {
                const settings = localStorage.getItem('pos_feature_settings');
                return settings ? JSON.parse(settings) : {
                    enableVoucher: false,
                    enableServiceCharge: true,
                    enableRounding: true,
                    enableDiscount: true,
                    enableTax: true
                };
            } catch (error) {
                console.error('Error retrieving POS feature settings:', error);
                return {
                    enableVoucher: false,
                    enableServiceCharge: true,
                    enableRounding: true,
                    enableDiscount: true,
                    enableTax: true
                };
            }
        };

        const savePOSFeatureSettings = (settings) => {
            try {
                localStorage.setItem('pos_feature_settings', JSON.stringify(settings));
                return true;
            } catch (error) {
                console.error('Error saving POS feature settings:', error);
                return false;
            }
        };

        const isVoucherEnabled = () => {
            const settings = getPOSFeatureSettings();
            return settings.enableVoucher;
        };

        const setVoucherEnabled = (enabled) => {
            const settings = getPOSFeatureSettings();
            settings.enableVoucher = enabled;
            return savePOSFeatureSettings(settings);
        };

        // Test functions
        const enableVoucher = () => {
            const success = setVoucherEnabled(true);
            if (success) {
                addTestResult('✅ Voucher enabled successfully', 'success');
                updateStatus();
            } else {
                addTestResult('❌ Failed to enable voucher', 'error');
            }
        };

        const disableVoucher = () => {
            const success = setVoucherEnabled(false);
            if (success) {
                addTestResult('✅ Voucher disabled successfully', 'success');
                updateStatus();
            } else {
                addTestResult('❌ Failed to disable voucher', 'error');
            }
        };

        const checkStatus = () => {
            const enabled = isVoucherEnabled();
            const settings = getPOSFeatureSettings();
            addTestResult(`🔍 Current voucher status: ${enabled ? 'ENABLED' : 'DISABLED'}`, 'success');
            addTestResult(`📋 Full settings: ${JSON.stringify(settings, null, 2)}`, 'success');
            updateStatus();
        };

        const clearSettings = () => {
            localStorage.removeItem('pos_feature_settings');
            addTestResult('🗑️ All settings cleared, reset to defaults', 'success');
            updateStatus();
        };

        const updateStatus = () => {
            const enabled = isVoucherEnabled();
            const statusElement = document.getElementById('voucherStatus');
            const lastUpdatedElement = document.getElementById('lastUpdated');
            
            statusElement.textContent = enabled ? 'ENABLED' : 'DISABLED';
            statusElement.className = enabled ? 'enabled' : 'disabled';
            lastUpdatedElement.textContent = new Date().toLocaleString();

            // Update all settings display
            const settings = getPOSFeatureSettings();
            const allSettingsElement = document.getElementById('allSettings');
            allSettingsElement.innerHTML = `
                <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;">
${JSON.stringify(settings, null, 2)}
                </pre>
            `;
        };

        const addTestResult = (message, type = 'success') => {
            const resultsElement = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type === 'error' ? 'error' : ''}`;
            resultDiv.innerHTML = `
                <strong>${new Date().toLocaleTimeString()}:</strong> ${message}
            `;
            resultsElement.insertBefore(resultDiv, resultsElement.firstChild);
            
            // Keep only last 10 results
            while (resultsElement.children.length > 10) {
                resultsElement.removeChild(resultsElement.lastChild);
            }
        };

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', () => {
            updateStatus();
            addTestResult('🚀 Voucher toggle test page loaded', 'success');
        });
    </script>
</body>
</html>
