# Rounding Methods Guide

## **Overview**
The Invoice Round Off setting now includes multiple rounding options to support different currencies and calculation methods, with Malaysian 5 Sen Rounding as the current default.

## **Available Rounding Methods**

### **1. Malaysian 5 Sen Rounding (Current)**
- **Value**: `malaysian_5sen`
- **Label**: "Malaysian 5 Sen Rounding"
- **Status**: ✅ **Currently Implemented**
- **Use Case**: Malaysian Ringgit (MYR) transactions
- **Rules**:
  - 0.01, 0.02 → rounds to 0.00
  - 0.03, 0.04, 0.05, 0.06, 0.07 → rounds to 0.05
  - 0.08, 0.09 → rounds to 0.10

### **2. Round Up (USD)**
- **Value**: `round_up`
- **Label**: "Round Up (USD)"
- **Status**: 🔄 **Future Implementation**
- **Use Case**: USD transactions where you always round up
- **Rules**: Always round to the next higher cent
- **Examples**:
  - $12.341 → $12.35
  - $12.301 → $12.31

### **3. Round Down (USD)**
- **Value**: `round_down`
- **Label**: "Round Down (USD)"
- **Status**: 🔄 **Future Implementation**
- **Use Case**: USD transactions where you always round down
- **Rules**: Always round to the lower cent
- **Examples**:
  - $12.349 → $12.34
  - $12.399 → $12.39

### **4. Round to Nearest (USD)**
- **Value**: `round_nearest`
- **Label**: "Round to Nearest (USD)"
- **Status**: 🔄 **Future Implementation**
- **Use Case**: Standard USD rounding
- **Rules**: Round to nearest cent (0.5 rounds up)
- **Examples**:
  - $12.344 → $12.34
  - $12.345 → $12.35
  - $12.346 → $12.35

### **5. Banker's Rounding (USD)**
- **Value**: `banker_rounding`
- **Label**: "Banker's Rounding (USD)"
- **Status**: 🔄 **Future Implementation**
- **Use Case**: Financial calculations requiring banker's rounding
- **Rules**: Round to nearest even number when exactly halfway
- **Examples**:
  - $12.345 → $12.34 (rounds to even)
  - $12.355 → $12.36 (rounds to even)

## **Current Implementation**

### **Default Selection**
```javascript
const roundoff = [
    { value: 'malaysian_5sen', label: 'Malaysian 5 Sen Rounding' },    // ✅ Current default
    { value: 'round_up', label: 'Round Up (USD)' },                    // 🔄 Future
    { value: 'round_down', label: 'Round Down (USD)' },                // 🔄 Future
    { value: 'round_nearest', label: 'Round to Nearest (USD)' },       // 🔄 Future
    { value: 'banker_rounding', label: 'Banker\'s Rounding (USD)' },   // 🔄 Future
];
```

### **Current Behavior**
- **Selected**: Malaysian 5 Sen Rounding (default)
- **Functional**: Only Malaysian rounding is implemented
- **Future Ready**: Dropdown prepared for additional methods
- **Change Handler**: Placeholder for future implementation

## **Future Implementation Plan**

### **Phase 1: USD Rounding Methods**
```javascript
// Future implementation in calculateRoundingAdjustment()
export const calculateRoundingAdjustment = (subTotalAmount, method = 'malaysian_5sen') => {
  switch (method) {
    case 'malaysian_5sen':
      // Current implementation
      return calculateMalaysian5SenRounding(subTotalAmount);
      
    case 'round_up':
      return calculateRoundUp(subTotalAmount);
      
    case 'round_down':
      return calculateRoundDown(subTotalAmount);
      
    case 'round_nearest':
      return calculateRoundNearest(subTotalAmount);
      
    case 'banker_rounding':
      return calculateBankerRounding(subTotalAmount);
      
    default:
      return calculateMalaysian5SenRounding(subTotalAmount);
  }
};
```

### **Phase 2: Currency Detection**
```javascript
// Future: Automatic method selection based on currency
const getRoundingMethodByCurrency = (currency) => {
  switch (currency) {
    case 'MYR':
      return 'malaysian_5sen';
    case 'USD':
      return 'round_nearest';
    case 'EUR':
      return 'round_nearest';
    default:
      return 'malaysian_5sen';
  }
};
```

### **Phase 3: Custom Rounding Rules**
```javascript
// Future: Support for custom rounding increments
const customRoundingOptions = [
  { value: 'custom_1cent', label: 'Round to 1 Cent' },
  { value: 'custom_5cent', label: 'Round to 5 Cents' },
  { value: 'custom_10cent', label: 'Round to 10 Cents' },
  { value: 'custom_quarter', label: 'Round to Quarter' },
];
```

## **Implementation Examples**

### **Round Up (USD)**
```javascript
const calculateRoundUp = (amount) => {
  const rounded = Math.ceil(amount * 100) / 100;
  return {
    grandTotalAmount: rounded,
    grandTotalAdjustmentAmount: amount - rounded
  };
};
```

### **Round Down (USD)**
```javascript
const calculateRoundDown = (amount) => {
  const rounded = Math.floor(amount * 100) / 100;
  return {
    grandTotalAmount: rounded,
    grandTotalAdjustmentAmount: amount - rounded
  };
};
```

### **Banker's Rounding (USD)**
```javascript
const calculateBankerRounding = (amount) => {
  const scaled = amount * 100;
  const rounded = Math.round(scaled) / 100;
  
  // Handle exact halfway cases (round to even)
  if (Math.abs(scaled - Math.round(scaled)) === 0.5) {
    const roundedScaled = Math.round(scaled);
    if (roundedScaled % 2 !== 0) {
      // If odd, round to even
      const adjusted = roundedScaled % 2 === 1 ? roundedScaled - 1 : roundedScaled + 1;
      return {
        grandTotalAmount: adjusted / 100,
        grandTotalAdjustmentAmount: amount - (adjusted / 100)
      };
    }
  }
  
  return {
    grandTotalAmount: rounded,
    grandTotalAdjustmentAmount: amount - rounded
  };
};
```

## **Benefits of Multi-Method Support**

### **1. Currency Flexibility**
- Support for different currency standards
- Automatic method selection based on currency
- Compliance with regional regulations

### **2. Business Requirements**
- Different rounding for different transaction types
- Configurable rounding policies
- Support for various accounting standards

### **3. International Expansion**
- Ready for multi-currency support
- Localized rounding rules
- Regional compliance

### **4. Future-Proof Design**
- Extensible architecture
- Easy to add new methods
- Backward compatibility maintained

## **Current Status**

- **✅ Malaysian 5 Sen**: Fully implemented and tested
- **🔄 USD Methods**: UI ready, implementation pending
- **📋 Dropdown**: Prepared with all future options
- **🎯 Default**: Malaysian rounding remains default

The system is now **future-ready** for USD and other currency rounding methods while maintaining full functionality with the current Malaysian 5 sen rounding system!
