# Malaysian Tax Calculation Implementation

## Overview
This document outlines the implementation of Malaysian tax calculation logic in the POS system, supporting tax inclusive, tax exclusive, and tax exempt calculations based on Malaysian tax standards.

## Key Features Implemented

### 1. **Tax Calculation Methods**
- **Tax Exclusive (isTaxExcl = true)**: Tax is added on top of the base amount
- **Tax Inclusive (isTaxExcl = false)**: Tax is extracted from the total amount
- **Tax Exempt (isTaxExempt = true)**: No tax calculation applied

### 2. **Malaysian Tax Standards**
- Precision: All calculations truncated to 5 decimal places initially
- Rounding: Final amounts rounded to 2 decimal places using `MidpointRounding.AwayFromZero` equivalent
- Separate handling for Sales Tax and Service Tax

## Implementation Details

### Core Calculation Functions

#### `calculateMalaysianTax()` in `orderStorage.js`
```javascript
// Tax Exclusive Formula:
salesTaxAmount = totalUnitAmount * (salesTaxRate / 100)
serviceTaxAmount = totalUnitAmount * (serviceTaxRate / 100)
subTotalAmount = totalUnitAmount + salesTaxAmount + serviceTaxAmount

// Tax Inclusive Formula:
salesTaxAmount = totalUnitAmount - (totalUnitAmount / (1 + salesTaxRate/100))
serviceTaxAmount = totalUnitAmount - (totalUnitAmount / (1 + serviceTaxRate/100))
subTotalAmount = totalUnitAmount (price already includes tax)
```

#### Enhanced `calculateOrderTotal()` Function
- Processes each order item individually with Malaysian tax logic
- Accumulates totals across all items
- Applies discounts on amount without tax (Malaysian standard)
- Handles service charges independently

### Product-Level Tax Settings

#### New Product Properties
```javascript
{
  isTaxExempt: boolean,           // Product is tax exempt
  isTaxExcl: boolean,             // Tax exclusive (true) or inclusive (false)
  salesTaxRate: number|null,      // Custom sales tax rate
  serviceTaxRate: number|null,    // Custom service tax rate
  customSalesTaxNoId: string,     // Custom sales tax ID
  customServiceTaxNoId: string    // Custom service tax ID
}
```

### Company Integration

#### `malaysianTax.js` Utility
- `getCompanyTaxConfig()`: Retrieves company tax settings from API
- `getProductTaxRates()`: Determines tax rates based on product and company settings
- `calculateItemMalaysianTax()`: Enhanced item-level tax calculation
- `calculateServiceCharges()`: Malaysian-compliant service charge calculation

### Service Charges Enhancement
- Service charges now calculated on amount without tax
- Async calculation to integrate with company profile API
- Fallback to original calculation if API fails

## Calculation Flow

### 1. **Item Level Calculation**
```
For each order item:
1. Get unit amount and quantity
2. Determine tax settings (exempt, exclusive/inclusive)
3. Get applicable tax rates (sales/service)
4. Apply Malaysian tax calculation
5. Return item totals
```

### 2. **Order Level Aggregation**
```
1. Sum all item calculations
2. Apply order-level discounts on amount without tax
3. Calculate service charges on discounted amount
4. Apply vouchers and rounding
5. Return final order total
```

### 3. **Tax Breakdown**
```javascript
{
  subtotal: number,           // Total including tax
  amountWOTax: number,        // Amount without tax
  salesTax: number,           // Total sales tax
  serviceTax: number,         // Total service tax
  tax: number,                // Total tax (sales + service)
  taxExclAmount: number,      // Tax exclusive amount
  taxInclAmount: number,      // Tax inclusive amount
  serviceCharges: number,     // Service charges
  discount: number,           // Discount amount
  total: number              // Final total
}
```

## Backend Logic Compatibility

### Matching Backend Implementation
The frontend implementation mirrors the backend C# logic:

```csharp
// Tax Exclusive (IsTaxExcl = true)
salesTaxAmount = totalUnitAmount * (salesTaxRate / 100)
subTotalAmount = totalUnitAmount + taxAmount

// Tax Inclusive (IsTaxExcl = false)  
salesTaxAmount = totalUnitAmount - (totalUnitAmount / (1 + salesTaxRate/100))
subTotalAmount = totalUnitAmount
```

### Key Differences Handled
- JavaScript number precision vs C# decimal precision
- Async API calls for company settings
- Frontend state management considerations

## Usage Examples

### Tax Exclusive Product (Default)
```
Product Price: RM 100.00
Sales Tax: 6%
Result:
- Amount without tax: RM 100.00
- Sales tax: RM 6.00
- Total: RM 106.00
```

### Tax Inclusive Product
```
Product Price: RM 106.00 (includes tax)
Sales Tax: 6%
Result:
- Amount without tax: RM 100.00
- Sales tax: RM 6.00
- Total: RM 106.00
```

### Tax Exempt Product
```
Product Price: RM 100.00
Tax Exempt: true
Result:
- Amount without tax: RM 100.00
- Sales tax: RM 0.00
- Total: RM 100.00
```

## Files Modified

### Core Files
- `src/core/utils/orderStorage.js` - Enhanced calculation functions
- `src/core/utils/malaysianTax.js` - New Malaysian tax utilities
- `src/feature-module/pos/pos.jsx` - Updated POS component

### Key Functions Updated
- `calculateOrderTotal()` - Now supports Malaysian tax logic
- `addProductToOrders()` - Includes tax settings for products
- `calculateServiceChargesFromProfile()` - Malaysian-compliant service charges
- `recalculateOrderTotal()` - Async support for API integration

## Testing Recommendations

### Test Cases
1. **Tax Exclusive Products**: Verify tax added on top
2. **Tax Inclusive Products**: Verify tax extracted from price
3. **Tax Exempt Products**: Verify no tax calculation
4. **Mixed Orders**: Products with different tax settings
5. **Service Charges**: Calculated on amount without tax
6. **Discounts**: Applied before tax calculation
7. **Company Settings**: Different tax rates from API

### Edge Cases
- Zero tax rates
- Missing company profile data
- API failures (fallback behavior)
- Rounding precision
- Large quantities and amounts

## Future Enhancements

### Potential Improvements
1. **Separate Sales/Service Tax UI**: Individual tax rate selection
2. **Product Tax Override**: UI for custom tax settings per product
3. **Tax Reports**: Detailed tax breakdown reporting
4. **Multi-Currency**: Tax calculation for different currencies
5. **Tax Validation**: Real-time validation against tax authority APIs

This implementation provides a robust foundation for Malaysian tax calculations while maintaining compatibility with existing POS functionality.
