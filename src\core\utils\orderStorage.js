/**
 * Utility functions for managing orders in local storage
 */

// Local storage keys
const ORDERS_STORAGE_KEY = 'pos_orders';
const DISCOUNT_SETTINGS_KEY = 'pos_discount_settings';
const PAYMENT_SUMMARY_KEY = 'pos_payment_summary';

/**
 * Get all orders from local storage
 * @returns {Array} Array of order items
 */
export const getOrders = () => {
  try {
    const orders = localStorage.getItem(ORDERS_STORAGE_KEY);
    return orders ? JSON.parse(orders) : [];
  } catch (error) {
    console.error('Error retrieving orders from local storage:', error);
    return [];
  }
};

/**
 * Save orders to local storage
 * @param {Array} orders - Array of order items
 */
export const saveOrders = (orders) => {
  try {
    localStorage.setItem(ORDERS_STORAGE_KEY, JSON.stringify(orders));
  } catch (error) {
    console.error('Error saving orders to local storage:', error);
  }
};

/**
 * Get product tax settings from product data
 * @param {Object} product - Product object
 * @returns {Object} Tax settings for the product
 */
export const getProductTaxSettings = (product) => {
  return {
    isTaxExempt: product.isTaxExempt || false,
    isTaxExcl: product.isTaxExcl !== undefined ? product.isTaxExcl : true, // Default to tax exclusive
    salesTaxRate: product.salesTaxRate || null,
    serviceTaxRate: product.serviceTaxRate || null,
    customSalesTaxNoId: product.customSalesTaxNoId || null,
    customServiceTaxNoId: product.customServiceTaxNoId || null
  };
};

/**
 * Add a product to orders with Malaysian tax settings
 * @param {Object} product - Product to add to orders
 * @returns {Array} Updated orders array
 */
export const addProductToOrders = (product) => {
  try {
    const orders = getOrders();

    // Check if product already exists in orders
    const existingProductIndex = orders.findIndex(item => item.id === product.id);

    if (existingProductIndex !== -1) {
      // If product exists, increment quantity
      orders[existingProductIndex].quantity += 1;
    } else {
      // If product doesn't exist, add it with quantity 1
      const defaultUom = product.productUOM?.find(uom => uom.isMainUom === true) ||
                         (product.productUOM?.length > 0 ? product.productUOM[0] : null);

      const price = defaultUom?.effectivedProductPrice?.price || 0;
      const formattedPrice = price.toFixed(product.currency?.precision || 2);

      // Get tax settings for the product
      const taxSettings = getProductTaxSettings(product);

      orders.push({
        id: product.id,
        name: product.name,
        image: product.image || product.img || "assets/img/products/pos-product-01.svg",
        price: formattedPrice,
        currencySymbol: product.currency?.symbol || '$',
        quantity: 1,
        uom: defaultUom?.uomPrimary?.name || 'Pcs',
        fractionQty: defaultUom?.effectivedProductPrice?.fractionQty || 1,
        // Malaysian tax settings
        isTaxExempt: taxSettings.isTaxExempt,
        isTaxExcl: taxSettings.isTaxExcl,
        salesTaxRate: taxSettings.salesTaxRate,
        serviceTaxRate: taxSettings.serviceTaxRate,
        customSalesTaxNoId: taxSettings.customSalesTaxNoId,
        customServiceTaxNoId: taxSettings.customServiceTaxNoId
      });
    }

    saveOrders(orders);
    return orders;
  } catch (error) {
    console.error('Error adding product to orders:', error);
    return getOrders();
  }
};

/**
 * Update product quantity in orders
 * @param {string|number} productId - ID of the product to update
 * @param {number} quantity - New quantity
 * @returns {Array} Updated orders array
 */
export const updateProductQuantity = (productId, quantity) => {
  try {
    const orders = getOrders();
    const productIndex = orders.findIndex(item => item.id === productId);

    if (productIndex !== -1) {
      if (quantity <= 0) {
        // Remove product if quantity is 0 or negative
        orders.splice(productIndex, 1);
      } else {
        // Update quantity
        orders[productIndex].quantity = quantity;
      }

      saveOrders(orders);
    }

    return orders;
  } catch (error) {
    console.error('Error updating product quantity:', error);
    return getOrders();
  }
};

/**
 * Remove a product from orders
 * @param {string|number} productId - ID of the product to remove
 * @returns {Array} Updated orders array
 */
export const removeProductFromOrders = (productId) => {
  try {
    const orders = getOrders();
    const updatedOrders = orders.filter(item => item.id !== productId);

    saveOrders(updatedOrders);
    return updatedOrders;
  } catch (error) {
    console.error('Error removing product from orders:', error);
    return getOrders();
  }
};

/**
 * Clear all orders
 * @returns {Array} Empty array
 */
export const clearOrders = () => {
  try {
    localStorage.removeItem(ORDERS_STORAGE_KEY);
    return [];
  } catch (error) {
    console.error('Error clearing orders:', error);
    return getOrders();
  }
};

/**
 * Save discount settings to local storage
 * @param {Object} settings - Discount settings object
 */
export const saveDiscountSettings = (settings) => {
  try {
    localStorage.setItem(DISCOUNT_SETTINGS_KEY, JSON.stringify(settings));
  } catch (error) {
    console.error('Error saving discount settings to local storage:', error);
  }
};

/**
 * Get discount settings from local storage
 * @returns {Object} Discount settings object
 */
export const getDiscountSettings = () => {
  try {
    const settings = localStorage.getItem(DISCOUNT_SETTINGS_KEY);
    return settings ? JSON.parse(settings) : {
      discountType: 'percentage',
      discountValue: 0,
      customPercentage: 0
    };
  } catch (error) {
    console.error('Error retrieving discount settings from local storage:', error);
    return {
      discountType: 'percentage',
      discountValue: 0,
      customPercentage: 0
    };
  }
};

/**
 * Save payment summary settings to local storage
 * @param {Object} settings - Payment summary settings object
 */
export const savePaymentSummary = (settings) => {
  try {
    localStorage.setItem(PAYMENT_SUMMARY_KEY, JSON.stringify(settings));
  } catch (error) {
    console.error('Error saving payment summary to local storage:', error);
  }
};

/**
 * Get payment summary settings from local storage
 * @returns {Object} Payment summary settings object
 */
export const getPaymentSummary = () => {
  try {
    const settings = localStorage.getItem(PAYMENT_SUMMARY_KEY);
    return settings ? JSON.parse(settings) : {
      serviceCharges: 0,
      tax: 0,
      taxRate: 0,
      voucher: 0,
      roundOff: true,
      roundOffAmount: 0
    };
  } catch (error) {
    console.error('Error retrieving payment summary from local storage:', error);
    return {
      serviceCharges: 0,
      tax: 0,
      taxRate: 0,
      voucher: 0,
      roundOff: true,
      roundOffAmount: 0
    };
  }
};

/**
 * Truncate number to 5 decimal places (Malaysian tax calculation standard)
 * @param {number} value - Value to truncate
 * @returns {number} Truncated value
 */
const truncateToFiveDecimalPlaces = (value) => {
  return Math.floor(value * 100000) / 100000;
};

/**
 * Calculate Malaysian tax for a single item (based on backend logic)
 * @param {number} unitAmount - Unit price
 * @param {number} quantity - Quantity
 * @param {number|null} salesTaxRate - Sales tax rate percentage
 * @param {number|null} serviceTaxRate - Service tax rate percentage
 * @param {boolean} isTaxExcl - True for tax exclusive, false for tax inclusive
 * @param {boolean} isTaxExempt - True if item is tax exempt
 * @returns {Object} Tax calculation results
 */
export const calculateMalaysianTax = (unitAmount, quantity, salesTaxRate, serviceTaxRate, isTaxExcl = true, isTaxExempt = false) => {
  // Truncate inputs to 5 decimal places
  unitAmount = truncateToFiveDecimalPlaces(unitAmount);
  quantity = truncateToFiveDecimalPlaces(quantity);

  let salesTaxAmount = 0.00;
  let serviceTaxAmount = 0.00;
  let taxExclAmount = 0.00;
  let taxInclAmount = 0.00;
  let totalUnitAmountWOTax = 0.00;
  let subTotalAmount = 0.00;
  let taxAmount = 0.00;

  // Calculate total amount before tax calculation
  const totalUnitAmount = Math.round(unitAmount * quantity * 100) / 100; // Round to 2 decimal places

  // If tax exempt, return amounts without tax
  if (isTaxExempt) {
    return {
      salesTaxAmount: 0.00,
      serviceTaxAmount: 0.00,
      taxExclAmount: 0.00,
      taxInclAmount: 0.00,
      totalUnitAmountWOTax: totalUnitAmount,
      totalUnitAmount: totalUnitAmount,
      subTotalAmount: totalUnitAmount,
      taxAmount: 0.00
    };
  }

  // Calculate tax amount based on Malaysian logic
  if (isTaxExcl) {
    // Tax Exclusive: Add tax on top of the amount
    salesTaxAmount = salesTaxRate != null ?
      Math.round(totalUnitAmount * (salesTaxRate / 100) * 100) / 100 : 0.00;
    serviceTaxAmount = serviceTaxRate != null ?
      Math.round(totalUnitAmount * (serviceTaxRate / 100) * 100) / 100 : 0.00;

    taxAmount = salesTaxAmount + serviceTaxAmount;
    taxExclAmount = taxAmount;
    totalUnitAmountWOTax = totalUnitAmount;
    subTotalAmount = totalUnitAmount + taxAmount;
  } else {
    // Tax Inclusive: Extract tax from the amount
    salesTaxAmount = salesTaxRate != null ?
      totalUnitAmount - Math.round(totalUnitAmount / (1 + (salesTaxRate / 100)) * 100) / 100 : 0.00;
    serviceTaxAmount = serviceTaxRate != null ?
      totalUnitAmount - Math.round(totalUnitAmount / (1 + (serviceTaxRate / 100)) * 100) / 100 : 0.00;

    taxAmount = salesTaxAmount + serviceTaxAmount;
    taxInclAmount = taxAmount;
    totalUnitAmountWOTax = totalUnitAmount - taxAmount;
    subTotalAmount = totalUnitAmount;
  }

  return {
    salesTaxAmount: parseFloat(salesTaxAmount.toFixed(2)),
    serviceTaxAmount: parseFloat(serviceTaxAmount.toFixed(2)),
    taxExclAmount: parseFloat(taxExclAmount.toFixed(2)),
    taxInclAmount: parseFloat(taxInclAmount.toFixed(2)),
    totalUnitAmountWOTax: parseFloat(totalUnitAmountWOTax.toFixed(2)),
    totalUnitAmount: parseFloat(totalUnitAmount.toFixed(2)),
    subTotalAmount: parseFloat(subTotalAmount.toFixed(2)),
    taxAmount: parseFloat(taxAmount.toFixed(2))
  };
};

/**
 * Calculate order total with Malaysian tax logic
 * @param {Object} options - Options for calculation (tax rate, discount, etc.)
 * @returns {Object} Object containing subtotal, tax, discount, and total
 */
export const calculateOrderTotal = (options = {}) => {
  try {
    const orders = getOrders();

    // Get saved discount settings if no options provided
    const savedSettings = getDiscountSettings();
    const savedPaymentSummary = getPaymentSummary();

    // Calculate item-level totals with Malaysian tax logic
    let orderSubtotal = 0;
    let orderTaxAmount = 0;
    let orderSalesTaxAmount = 0;
    let orderServiceTaxAmount = 0;
    let orderTaxExclAmount = 0;
    let orderTaxInclAmount = 0;
    let orderAmountWOTax = 0;

    // Process each order item
    orders.forEach(item => {
      const unitAmount = parseFloat(item.price) || 0;
      const quantity = parseFloat(item.quantity) || 0;

      // Get tax settings from item or use order-level defaults
      const isTaxExempt = item.isTaxExempt || false;
      const isTaxExcl = item.isTaxExcl !== undefined ? item.isTaxExcl : true; // Default to tax exclusive

      // Get tax rates - for now use single tax rate, can be enhanced for separate sales/service tax
      const taxRate = parseFloat(options.taxRate !== undefined ? options.taxRate : savedPaymentSummary.taxRate || 0);
      const salesTaxRate = item.salesTaxRate || taxRate;
      const serviceTaxRate = item.serviceTaxRate || 0; // Separate service tax if needed

      // Calculate Malaysian tax for this item
      const itemTaxCalc = calculateMalaysianTax(
        unitAmount,
        quantity,
        salesTaxRate,
        serviceTaxRate,
        isTaxExcl,
        isTaxExempt
      );

      // Accumulate totals
      orderSubtotal += itemTaxCalc.subTotalAmount;
      orderTaxAmount += itemTaxCalc.taxAmount;
      orderSalesTaxAmount += itemTaxCalc.salesTaxAmount;
      orderServiceTaxAmount += itemTaxCalc.serviceTaxAmount;
      orderTaxExclAmount += itemTaxCalc.taxExclAmount;
      orderTaxInclAmount += itemTaxCalc.taxInclAmount;
      orderAmountWOTax += itemTaxCalc.totalUnitAmountWOTax;
    });

    // Get discount settings
    const discountType = options.discountType || savedSettings.discountType || 'percentage';
    let discountValue = parseFloat(options.discountValue || savedSettings.discountValue || 0);

    // Use custom percentage if available and discount type is percentage
    if (discountType === 'percentage' && options.customPercentage !== undefined) {
      discountValue = parseFloat(options.customPercentage);
    } else if (discountType === 'percentage' && savedSettings.customPercentage) {
      discountValue = parseFloat(savedSettings.customPercentage);
    }

    // Calculate discount amount on amount without tax (Malaysian standard)
    let discountAmount = 0;
    if (discountType === 'flat') {
      discountAmount = discountValue;
    } else if (discountType === 'percentage') {
      discountAmount = (orderAmountWOTax * discountValue) / 100;
    }

    // Ensure discount doesn't exceed amount without tax
    discountAmount = Math.min(discountAmount, orderAmountWOTax);

    // Get service charges from options or use saved settings
    const serviceCharges = parseFloat(options.serviceCharges !== undefined ? options.serviceCharges : savedPaymentSummary.serviceCharges || 0);

    // Get voucher amount from options or use saved settings
    const voucherAmount = parseFloat(options.voucher !== undefined ? options.voucher : savedPaymentSummary.voucher || 0);

    // Calculate final total
    // Formula: Subtotal - Discount + Service Charges - Voucher
    let total = orderSubtotal - discountAmount + serviceCharges - voucherAmount;

    // Apply rounding if enabled
    const roundOff = options.roundOff !== undefined ? options.roundOff : savedPaymentSummary.roundOff;
    let roundOffAmount = 0;

    if (roundOff) {
      // Round to nearest whole number
      const roundedTotal = Math.round(total);
      roundOffAmount = roundedTotal - total;
      total = roundedTotal;
    }

    // Ensure total is not negative
    total = Math.max(0, total);

    return {
      subtotal: parseFloat(orderSubtotal.toFixed(2)),
      tax: parseFloat(orderTaxAmount.toFixed(2)),
      salesTax: parseFloat(orderSalesTaxAmount.toFixed(2)),
      serviceTax: parseFloat(orderServiceTaxAmount.toFixed(2)),
      taxExclAmount: parseFloat(orderTaxExclAmount.toFixed(2)),
      taxInclAmount: parseFloat(orderTaxInclAmount.toFixed(2)),
      amountWOTax: parseFloat(orderAmountWOTax.toFixed(2)),
      taxRate: parseFloat(options.taxRate !== undefined ? options.taxRate : savedPaymentSummary.taxRate || 0),
      discount: parseFloat(discountAmount.toFixed(2)),
      serviceCharges: parseFloat(serviceCharges.toFixed(2)),
      voucher: parseFloat(voucherAmount.toFixed(2)),
      roundOffAmount: parseFloat(roundOffAmount.toFixed(2)),
      total: parseFloat(total.toFixed(2)),
      discountType,
      discountValue,
      taxableAmount: parseFloat((orderAmountWOTax - discountAmount).toFixed(2))
    };
  } catch (error) {
    console.error('Error calculating order total:', error);
    return {
      subtotal: 0,
      tax: 0,
      salesTax: 0,
      serviceTax: 0,
      taxExclAmount: 0,
      taxInclAmount: 0,
      amountWOTax: 0,
      taxRate: 0,
      discount: 0,
      serviceCharges: 0,
      voucher: 0,
      roundOffAmount: 0,
      total: 0,
      discountType: 'percentage',
      discountValue: 0,
      taxableAmount: 0
    };
  }
};
