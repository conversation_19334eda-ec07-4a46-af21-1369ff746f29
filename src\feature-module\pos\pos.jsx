import React, { useEffect, useState, useCallback } from "react";
import { Link, useLocation } from "react-router-dom";
import ImageWithBasePath from "../../core/img/imagewithbasebath";
import Select from "react-select"
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import PosModals from "../../core/modals/pos-modal/posModals";
import CartCounter from "../../core/common/counter/counter";
import categoryService from "../../core/services/category.service";
import productService from "../../core/services/product.service";
import {
  getOrders,
  addProductToOrders,
  updateProductQuantity,
  removeProductFromOrders,
  clearOrders,
  calculateOrderTotal,
  getDiscountSettings,
  saveDiscountSettings,
  getPaymentSummary,
  savePaymentSummary
} from "../../core/utils/orderStorage";
import {
  getCustomersForSelect,
  getSelectedCustomer,
  setSelectedCustomer,
  getOrdersForCustomer
} from "../../core/utils/customerStorage";
import {
  getCurrentOrderPayments,
  calculateRemainingBalance,
  formatPaymentCurrency,
  clearCurrentOrderPayments
} from "../../core/utils/paymentStorage";
import paymentMethodsService from "../../core/services/paymentMethods.service";
import { initializeTaxCategories } from "../../core/utils/taxCategories";
import { formatCurrency } from "../../core/utils/currency";
import CompanyService from "../../core/services/company.service";


import "./pos-custom.css";


const Pos = () => {
 const [activeTab , setActiveTab] = useState('all')
 const Location = useLocation();
 const [categories, setCategories] = useState([])
 const [products, setProducts] = useState([])
 const [filteredProducts, setFilteredProducts] = useState([])
 const [loadingCategories, setLoadingCategories] = useState(false)
 const [loadingProducts, setLoadingProducts] = useState(false)
 const [categoryError, setCategoryError] = useState(null)
 const [productError, setProductError] = useState(null)
 const [searchQuery, setSearchQuery] = useState('')
 const [orderItems, setOrderItems] = useState([])
 const [orderTotal, setOrderTotal] = useState({
   subtotal: '0.00',
   tax: '0.00',
   taxRate: 0,
   discount: '0.00',
   serviceCharges: '0.00',
   voucher: '0.00',
   roundOffAmount: '0.00',
   total: '0.00',
   discountType: 'percentage',
   discountValue: 0
 })
 const [discountSettings, setDiscountSettings] = useState({
   discountType: 'percentage',
   discountValue: 0,
   customPercentage: 0
 })
 const [paymentSummary, setPaymentSummary] = useState({
   serviceCharges: 0,
   tax: 0,
   taxRate: 0,
   voucher: 0,
   roundOff: true,
   roundOffAmount: 0
 })
 const [currentOrderPayments, setCurrentOrderPayments] = useState([])
 const [paymentBalance, setPaymentBalance] = useState({
   orderTotal: 0,
   totalPaid: 0,
   remaining: 0,
   isFullyPaid: false,
   overpaid: 0
 })
 const [paymentMethods, setPaymentMethods] = useState([])
 const [isLoadingPaymentMethods, setIsLoadingPaymentMethods] = useState(true)

 // Company profile state for service charges
 const [companyProfile, setCompanyProfile] = useState(null)

 // Customer state
 const [customerOptions, setCustomerOptions] = useState([])
 const [selectedCustomer, setSelectedCustomerState] = useState(null)

 // Fetch categories from API
 useEffect(() => {
   const fetchCategories = async () => {
     try {
       setLoadingCategories(true)
       const response = await categoryService.getCategories()
       if (response.isSuccessful && response.result) {
         // The categories are in the result property
         setCategories(response.result)
       } else {
         setCategoryError('Failed to fetch categories')
       }
     } catch (err) {
       console.error('Error fetching categories:', err)
       setCategoryError(err.message || 'Failed to fetch categories')
     } finally {
       setLoadingCategories(false)
     }
   }

   fetchCategories()
 }, [])

 // Fetch products from API
 useEffect(() => {
   const fetchProducts = async () => {
     try {
       setLoadingProducts(true)
       const response = await productService.getProducts()
       if (response.isSuccessful && response.result) {
         // The products are in the result property
         setProducts(response.result)
         setFilteredProducts(response.result)
       } else {
         setProductError('Failed to fetch products')
       }
     } catch (err) {
       console.error('Error fetching products:', err)
       setProductError(err.message || 'Failed to fetch products')
     } finally {
       setLoadingProducts(false)
     }
   }

   fetchProducts()
 }, [])

 // Filter products when category changes
 useEffect(() => {
   if (activeTab === 'all') {
     setFilteredProducts(products)
   } else {
     // Log for debugging
     console.log('Active Tab:', activeTab);
     console.log('Products:', products);

     // Check different ways products might be associated with categories
     const productsWithMatchingId = products.filter(product => product.productCategoryId === activeTab);
     console.log('Products with matching productCategoryId:', productsWithMatchingId);

     const productsWithMatchingCategory = products.filter(product =>
       product.productCategory?.id === activeTab ||
       product.categoryId === activeTab ||
       product.category?.id === activeTab
     );
     console.log('Products with matching category object:', productsWithMatchingCategory);

     // Use a more flexible approach to filter products
     const filtered = products.filter(product =>
       product.productCategoryId === activeTab ||
       product.productCategory?.id === activeTab ||
       product.categoryId === activeTab ||
       product.category?.id === activeTab
     );

     setFilteredProducts(filtered)
   }
 }, [activeTab, products])

 // Handle search functionality
 useEffect(() => {
   if (searchQuery.trim() === '') {
     // If search query is empty, reset to category filter
     if (activeTab === 'all') {
       setFilteredProducts(products)
     } else {
       // Use the same flexible approach as in the category filter
       const filtered = products.filter(product =>
         product.productCategoryId === activeTab ||
         product.productCategory?.id === activeTab ||
         product.categoryId === activeTab ||
         product.category?.id === activeTab
       )
       setFilteredProducts(filtered)
     }
   } else {
     // Filter by search query
     const searchResults = products.filter(product =>
       product.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
       product.code?.toLowerCase().includes(searchQuery.toLowerCase()) ||
       product.description?.toLowerCase().includes(searchQuery.toLowerCase())
     )
     setFilteredProducts(searchResults)
   }
 }, [searchQuery, products, activeTab])

 // Handle search input change
 const handleSearchChange = (e) => {
   setSearchQuery(e.target.value)
 }

 // Fetch payment methods from API
 useEffect(() => {
   const fetchPaymentMethods = async () => {
     try {
       setIsLoadingPaymentMethods(true);
       const methods = await paymentMethodsService.getActivePaymentMethods();
       setPaymentMethods(methods);
     } catch (error) {
       console.error('Error fetching payment methods:', error);
       // Use fallback methods if API fails
       setPaymentMethods(paymentMethodsService.getFallbackPaymentMethods());
     } finally {
       setIsLoadingPaymentMethods(false);
     }
   };

   fetchPaymentMethods();
 }, []);

 // Fetch company profile for service charges
 useEffect(() => {
   const fetchCompanyProfile = async () => {
     try {
       // Force refresh company profile to get latest data
       const profile = await CompanyService.refreshCompanyProfile();
       setCompanyProfile(profile);
       console.log('Company profile loaded for POS:', profile);
     } catch (error) {
       console.error('Error fetching company profile:', error);
       // Fallback to cached data if API fails
       try {
         const cachedProfile = CompanyService.getCachedCompanyInfo();
         if (cachedProfile) {
           setCompanyProfile(cachedProfile);
           console.log('Using cached company profile as fallback');
         }
       } catch (cacheError) {
         console.error('Error loading cached company profile:', cacheError);
       }
     }
   };

   // Listen for company profile refresh events
   const handleCompanyProfileRefresh = async () => {
     console.log('Received company profile refresh event in POS');
     await fetchCompanyProfile();
   };

   // Add event listener
   window.addEventListener('companyProfileRefreshed', handleCompanyProfileRefresh);

   // Initial fetch
   fetchCompanyProfile();

   // Cleanup event listener
   return () => {
     window.removeEventListener('companyProfileRefreshed', handleCompanyProfileRefresh);
   };
 }, []);

 // Load customers and selected customer
 useEffect(() => {
   const loadCustomers = async () => {
     try {
       const customers = await getCustomersForSelect();
       setCustomerOptions(customers);

       const selected = await getSelectedCustomer();
       setSelectedCustomerState(selected);
     } catch (error) {
       console.error('Error loading customers:', error);
       // Fallback to empty state
       setCustomerOptions([]);
       setSelectedCustomerState(null);
     }
   };

   loadCustomers();
 }, []);

 // Listen for customer updates from modals
 useEffect(() => {
   const handleCustomerUpdate = async (event) => {
     const { customer } = event.detail;

     try {
       // Reload customer options
       const customers = await getCustomersForSelect(false); // Force refresh
       setCustomerOptions(customers);

       // Update selected customer
       setSelectedCustomerState(customer);
     } catch (error) {
       console.error('Error updating customers:', error);
     }
   };

   const handleOrderCleared = () => {
     // Reload orders when order is cleared
     const savedOrders = getOrders();
     setOrderItems(savedOrders);

     // Recalculate total
     const total = calculateOrderTotal({
       discountType: discountSettings.discountType,
       discountValue: discountSettings.discountValue,
       customPercentage: discountSettings.customPercentage,
       serviceCharges: paymentSummary.serviceCharges,
       taxRate: paymentSummary.taxRate,
       voucher: paymentSummary.voucher,
       roundOff: paymentSummary.roundOff
     });
     setOrderTotal(total);
   };

   const handleOrderLoaded = (event) => {
     const { order } = event.detail;

     // Reload orders when order is loaded
     const savedOrders = getOrders();
     setOrderItems(savedOrders);

     // Update discount settings
     if (order.discountSettings) {
       setDiscountSettings(order.discountSettings);
     }

     // Update payment summary
     if (order.paymentSummary) {
       setPaymentSummary(order.paymentSummary);
     }

     // Recalculate total
     const total = calculateOrderTotal({
       discountType: order.discountSettings?.discountType || discountSettings.discountType,
       discountValue: order.discountSettings?.discountValue || discountSettings.discountValue,
       customPercentage: order.discountSettings?.customPercentage || discountSettings.customPercentage,
       serviceCharges: order.paymentSummary?.serviceCharges || paymentSummary.serviceCharges,
       taxRate: order.paymentSummary?.taxRate || paymentSummary.taxRate,
       voucher: order.paymentSummary?.voucher || paymentSummary.voucher,
       roundOff: order.paymentSummary?.roundOff || paymentSummary.roundOff
     });
     setOrderTotal(total);
   };

   window.addEventListener('customerUpdated', handleCustomerUpdate);
   window.addEventListener('orderCleared', handleOrderCleared);
   window.addEventListener('orderLoaded', handleOrderLoaded);

   return () => {
     window.removeEventListener('customerUpdated', handleCustomerUpdate);
     window.removeEventListener('orderCleared', handleOrderCleared);
     window.removeEventListener('orderLoaded', handleOrderLoaded);
   };
 }, [discountSettings, paymentSummary]);

 // Handle customer selection change
 const handleCustomerChange = (selectedOption) => {
   if (selectedOption && selectedOption.customer) {
     setSelectedCustomer(selectedOption.customer);
     setSelectedCustomerState(selectedOption.customer);
   }
 };

 // Handle save order button click
 const handleSaveOrderClick = () => {
   if (!selectedCustomer || selectedCustomer.id === 'walk-in') {
     alert('Please select a customer before saving the order.');
     return;
   }

   if (!orderItems || orderItems.length === 0) {
     alert('No items in the order to save.');
     return;
   }

   // Open save order modal
   const modal = new window.bootstrap.Modal(document.getElementById('save-order'));
   modal.show();
 };

 // Handle view customer orders button click
 const handleViewCustomerOrdersClick = () => {
   if (!selectedCustomer || selectedCustomer.id === 'walk-in') {
     alert('Please select a customer to view their orders.');
     return;
   }

   // Load customer orders and open modal
   const orders = getOrdersForCustomer(selectedCustomer.id);

   // Trigger event to update modal state
   window.dispatchEvent(new CustomEvent('loadCustomerOrders', {
     detail: { customer: selectedCustomer, orders: orders }
   }));

   // Open customer orders modal
   const modal = new window.bootstrap.Modal(document.getElementById('customer-orders'));
   modal.show();
 };

 // Function to calculate service charges based on company profile using Malaysian logic
 const calculateServiceChargesFromProfile = useCallback(() => {
   try {
     if (!companyProfile || !companyProfile.result) {
       return 0;
     }

     const serviceChargePercentage = companyProfile.result.serviceCharge;

     // If serviceCharge is null, 0, or not provided, return 0
     if (!serviceChargePercentage || serviceChargePercentage === 0) {
       return 0;
     }

     // Calculate subtotal without service charges to avoid circular dependency
     const total = calculateOrderTotal({
       discountType: discountSettings.discountType,
       discountValue: discountSettings.discountValue,
       customPercentage: discountSettings.customPercentage,
       serviceCharges: 0, // Don't include service charges in subtotal calculation
       taxRate: paymentSummary.taxRate,
       voucher: paymentSummary.voucher,
       roundOff: paymentSummary.roundOff
     });

     // Use Malaysian service charges calculation on amount without tax (if available)
     const baseAmount = total.amountWOTax || total.subtotal;
     const serviceCharges = (baseAmount * serviceChargePercentage) / 100;
     return parseFloat(serviceCharges.toFixed(2));
   } catch (error) {
     console.error('Error calculating service charges:', error);
     return 0;
   }
 }, [companyProfile, discountSettings, paymentSummary]);

 // Function to get service charge percentage from company profile
 const getServiceChargePercentage = () => {
   if (!companyProfile || !companyProfile.result) {
     return 0;
   }
   return companyProfile.result.serviceCharge || 0;
 };

 // Function to check if service charges should be shown
 const shouldShowServiceCharges = () => {
   const percentage = getServiceChargePercentage();
   return percentage > 0;
 };

 // Function to check if tax should be shown
 const shouldShowTax = () => {
   return paymentSummary.taxRate > 0;
 };

 // Function to get current tax amount (calculated dynamically)
 const getCurrentTaxAmount = () => {
   return calculateOrderTotal({
     discountType: discountSettings.discountType,
     discountValue: discountSettings.discountValue,
     customPercentage: discountSettings.customPercentage,
     serviceCharges: paymentSummary.serviceCharges,
     taxRate: paymentSummary.taxRate,
     voucher: paymentSummary.voucher,
     roundOff: paymentSummary.roundOff
   }).tax;
 };

 // Function to recalculate order total (independent tax and service charge calculations)
 const recalculateOrderTotal = useCallback(() => {
   try {
     // Calculate service charges independently
     const calculatedServiceCharges = calculateServiceChargesFromProfile();

     // Calculate order total with current settings
     const total = calculateOrderTotal({
       discountType: discountSettings.discountType,
       discountValue: discountSettings.discountValue,
       customPercentage: discountSettings.customPercentage,
       serviceCharges: calculatedServiceCharges,
       taxRate: paymentSummary.taxRate,
       voucher: paymentSummary.voucher,
       roundOff: paymentSummary.roundOff
     });

     // Update order total
     setOrderTotal(total);

     // Update payment summary only if service charges changed
     if (calculatedServiceCharges !== paymentSummary.serviceCharges) {
       const newPaymentSummary = { ...paymentSummary, serviceCharges: calculatedServiceCharges };
       setPaymentSummary(newPaymentSummary);
       savePaymentSummary(newPaymentSummary);
     }

     return total;
   } catch (error) {
     console.error('Error recalculating order total:', error);
     // Fallback to synchronous calculation
     const total = calculateOrderTotal({
       discountType: discountSettings.discountType,
       discountValue: discountSettings.discountValue,
       customPercentage: discountSettings.customPercentage,
       serviceCharges: paymentSummary.serviceCharges,
       taxRate: paymentSummary.taxRate,
       voucher: paymentSummary.voucher,
       roundOff: paymentSummary.roundOff
     });
     setOrderTotal(total);
     return total;
   }
 }, [discountSettings, paymentSummary, calculateServiceChargesFromProfile]);



 // Load orders, discount settings, payment summary, and payments from local storage on component mount
 useEffect(() => {
   const savedOrders = getOrders();
   setOrderItems(savedOrders);

   // Load discount settings
   const savedDiscountSettings = getDiscountSettings();
   setDiscountSettings(savedDiscountSettings);

   // Load payment summary settings
   const savedPaymentSummary = getPaymentSummary();
   setPaymentSummary(savedPaymentSummary);

   // Load current order payments
   const savedPayments = getCurrentOrderPayments();
   setCurrentOrderPayments(savedPayments);

   // Initialize tax categories from API with enhanced error handling
   const initializeTaxWithFallback = async () => {
     try {
       console.log('Initializing tax categories for POS...');
       await initializeTaxCategories();

       // If no tax rate is set, try to set a default one
       if (savedPaymentSummary.taxRate === 0) {
         const { getDefaultTaxCategory } = await import('../../core/utils/taxCategories');
         const defaultTax = await getDefaultTaxCategory(false);
         if (defaultTax) {
           const newPaymentSummary = { ...savedPaymentSummary, taxRate: defaultTax.rate };
           setPaymentSummary(newPaymentSummary);
           savePaymentSummary(newPaymentSummary);
           console.log(`Set default tax rate: ${defaultTax.rate}% (${defaultTax.name})`);
         }
       }
     } catch (error) {
       console.warn('Failed to initialize tax categories from API, using local fallback:', error);
       // Fallback to synchronous initialization
       const { initializeTaxCategoriesSync } = await import('../../core/utils/taxCategories');
       initializeTaxCategoriesSync();
     }
   };
   initializeTaxWithFallback();

   // Calculate and set order total with initial settings (service charges will be updated when company profile loads)
   const total = calculateOrderTotal({
     discountType: savedDiscountSettings.discountType,
     discountValue: savedDiscountSettings.discountValue,
     customPercentage: savedDiscountSettings.customPercentage,
     serviceCharges: savedPaymentSummary.serviceCharges,
     taxRate: savedPaymentSummary.taxRate,
     voucher: savedPaymentSummary.voucher,
     roundOff: savedPaymentSummary.roundOff
   });
   setOrderTotal(total);

   // Calculate payment balance
   const balance = calculateRemainingBalance(parseFloat(total.total));
   setPaymentBalance(balance);
 }, []);

 // Listen for order total updates from modals
 useEffect(() => {
   const handleOrderTotalUpdate = (event) => {
     const { orderTotal, discountSettings: newDiscountSettings, paymentSummary: newPaymentSummary } = event.detail;

     // Update states
     setOrderTotal(orderTotal);
     setDiscountSettings(newDiscountSettings);
     setPaymentSummary(newPaymentSummary);

     // Update payment balance
     const balance = calculateRemainingBalance(parseFloat(orderTotal.total));
     setPaymentBalance(balance);
   };

   window.addEventListener('orderTotalUpdated', handleOrderTotalUpdate);

   return () => {
     window.removeEventListener('orderTotalUpdated', handleOrderTotalUpdate);
   };
 }, []);

 // Listen for payment updates from modals
 useEffect(() => {
   const handlePaymentUpdate = (event) => {
     const { payments } = event.detail;
     setCurrentOrderPayments(payments);

     // Update payment balance
     const balance = calculateRemainingBalance(parseFloat(orderTotal.total));
     setPaymentBalance(balance);
   };

   window.addEventListener('paymentUpdated', handlePaymentUpdate);

   return () => {
     window.removeEventListener('paymentUpdated', handlePaymentUpdate);
   };
 }, [orderTotal.total]);

 // Handle product click to toggle in order list
 const handleProductClick = async (product, event) => {
   try {
     // Check if product is already in order list
     const isInOrderList = orderItems.some(item => item.id === product.id);

     let updatedOrders;

     if (isInOrderList) {
       // If product is already in order list, remove it
       updatedOrders = removeProductFromOrders(product.id);
     } else {
       // If product is not in order list, add it
       updatedOrders = addProductToOrders(product);
     }

     // Update state
     setOrderItems(updatedOrders);

     // Recalculate order total with independent tax and service charge calculations (async)
     await recalculateOrderTotal();

     // Add visual feedback
     if (event && event.currentTarget) {
       const element = event.currentTarget;

       // Add pulse animation class
       element.classList.add("pulse-animation");

       // Remove animation class after animation completes
       setTimeout(() => {
         element.classList.remove("pulse-animation");
       }, 300);
     }
   } catch (error) {
     console.error('Error handling product click:', error);
   }
 }

 // Handle quantity change
 const handleQuantityChange = async (productId, newQuantity) => {
   try {
     // Update product quantity in local storage
     const updatedOrders = updateProductQuantity(productId, newQuantity);

     // Update state
     setOrderItems(updatedOrders);

     // Recalculate order total with independent tax and service charge calculations (async)
     await recalculateOrderTotal();
   } catch (error) {
     console.error('Error handling quantity change:', error);
   }
 }

 // Handle product removal
 const handleRemoveProduct = async (productId) => {
   try {
     // Remove product from local storage
     const updatedOrders = removeProductFromOrders(productId);

     // Update state
     setOrderItems(updatedOrders);

     // Recalculate order total with independent tax and service charge calculations (async)
     await recalculateOrderTotal();
   } catch (error) {
     console.error('Error handling product removal:', error);
   }
 }

 // Get modal target for payment method
 const getPaymentModalTarget = (paymentMethod) => {
   const modalMap = {
     'cash': '#payment-cash',
     'card': '#payment-card',
     'points': '#payment-points',
     'deposit': '#payment-deposit',
     'cheque': '#payment-cheque',
     'check': '#payment-cheque',
     'gift card': '#gift-payment',
     'giftcard': '#gift-payment',
     'scan': '#scan-payment',
     'qr code': '#scan-payment',
     'split bill': '#split-payment',
     'split': '#split-payment'
   };

   const methodKey = paymentMethod.value?.toLowerCase() || paymentMethod.label?.toLowerCase() || '';
   return modalMap[methodKey] || '#payment-cash'; // Default to cash modal
 };

 // Handle clear all products
 const handleClearOrders = () => {
   // Clear orders from local storage
   clearOrders();

   // Clear payments from local storage
   clearCurrentOrderPayments();

   // Update state
   setOrderItems([]);
   setCurrentOrderPayments([]);

   // Reset order total
   setOrderTotal({
     subtotal: '0.00',
     tax: '0.00',
     taxRate: 0,
     discount: '0.00',
     serviceCharges: '0.00',
     voucher: '0.00',
     roundOffAmount: '0.00',
     total: '0.00',
     discountType: 'percentage',
     discountValue: 0
   });

   // Reset payment balance
   setPaymentBalance({
     orderTotal: 0,
     totalPaid: 0,
     remaining: 0,
     isFullyPaid: false,
     overpaid: 0
   });
 }
  const settings = {
    dots: false,
    autoplay: false,
    slidesToShow: 8, // Increased from 6 to 8 to show more categories
    margin: 0,
    speed: 500,
    infinite: false,
    arrows: true,
    centerMode: false,
    centerPadding: '0',
    responsive: [
      {
        breakpoint: 1200,
        settings: {
          slidesToShow: 6,
        },
      },
      {
        breakpoint: 992,
        settings: {
          slidesToShow: 5,
        },
      },
      {
        breakpoint: 800,
        settings: {
          slidesToShow: 4,
        },
      },
      {
        breakpoint: 776,
        settings: {
          slidesToShow: 3,
        },
      },
      {
        breakpoint: 567,
        settings: {
          slidesToShow: 2,
        },
      },
    ],
  };


  // const gstOptions = [
  //   { value: "choose", label: "Choose" },
  //   { value: "gst5", label: "GST 5%" },
  //   { value: "gst10", label: "GST 10%" },
  //   { value: "gst15", label: "GST 15%" },
  //   { value: "gst20", label: "GST 20%" },
  //   { value: "gst25", label: "GST 25%" },
  //   { value: "gst30", label: "GST 30%" },
  // ];
  // const numericOptions = [
  //   { value: "0", label: "0" },
  //   { value: "15", label: "15" },
  //   { value: "20", label: "20" },
  //   { value: "25", label: "25" },
  //   { value: "30", label: "30" },
  // ];

  // const percentageOptions = [
  //   { value: "0%", label: "0%" },
  //   { value: "10%", label: "10%" },
  //   { value: "15%", label: "15%" },
  //   { value: "20%", label: "20%" },
  //   { value: "25%", label: "25%" },
  //   { value: "30%", label: "30%" },
  // ];
// Update order total when order items change
useEffect(() => {
  // Recalculate order total with independent tax and service charge calculations
  const total = recalculateOrderTotal();

  // Update payment balance
  const balance = calculateRemainingBalance(parseFloat(total.total));
  setPaymentBalance(balance);
}, [orderItems, discountSettings.discountType, discountSettings.discountValue, discountSettings.customPercentage,
    paymentSummary.taxRate, paymentSummary.voucher, paymentSummary.roundOff, companyProfile, recalculateOrderTotal]);

// Update order total when discount settings or payment summary change (tax changes only)
useEffect(() => {
  if (orderItems.length > 0) {
    // Recalculate order total with independent tax and service charge calculations
    recalculateOrderTotal();

    // Save settings to local storage
    saveDiscountSettings(discountSettings);
  }
}, [discountSettings, paymentSummary.taxRate, paymentSummary.voucher, paymentSummary.roundOff,
    orderItems.length, companyProfile, recalculateOrderTotal]);

useEffect(() => {
    // Add page class
    document.body.classList.add("pos-page");

    return () => {
        document.body.classList.remove("pos-page");
    }
}, [Location.pathname])

  return (
    <div className="main-wrapper pos-default">
      <div className="page-wrapper pos-pg-wrapper ms-0">
        <div className="content pos-design p-0">
          <div className="row align-items-start pos-wrapper">
            {/* Products */}
            <div className="col-md-12 col-lg-7 col-xl-8">
              <div className="pos-categories tabs_wrapper pb-0">
                <div className="card pos-button">
                  <div className="d-flex align-items-center flex-wrap">
                    <Link
                      to="#"
                      className="btn btn-teal btn-md mb-xs-3"
                      data-bs-toggle="modal"
                      data-bs-target="#orders"
                    >
                      <i className="ti ti-shopping-cart me-1" />
                      View Orders
                    </Link>
                    <Link
                      to="#"
                      className="btn btn-md btn-indigo"
                      data-bs-toggle="modal"
                      data-bs-target="#reset"
                    >
                      <i className="ti ti-reload me-1" />
                      Reset
                    </Link>
                    <Link
                      to="#"
                      className="btn btn-md btn-info"
                      data-bs-toggle="modal"
                      data-bs-target="#recents"
                    >
                      <i className="ti ti-refresh-dot me-1" />
                      Transaction
                    </Link>
                  </div>
                </div>
                <div className="d-flex align-items-center justify-content-between">
                  <h4 className="mb-3">Categories</h4>
                </div>
                <Slider {...settings} className={`tabs owl-carousel pos-category ${categories.length === 0 ? 'no-data' : ''}`}>
                  {/* Always show "All Categories" option */}
                  <div onClick={()=>setActiveTab('all')} className={`owl-item ${activeTab === 'all' ? 'active' : ''}`} id="all" style={{ marginLeft: categories.length === 0 ? '0' : '' }}>
                    <Link to="#">
                      <ImageWithBasePath
                        src="assets/img/categories/category-01.svg"
                        alt="Categories"
                      />
                    </Link>
                    <h6>
                      <Link to="#">All Categories</Link>
                    </h6>
                    <span>{products.length} Items</span>
                  </div>

                  {/* Loading indicator */}
                  {loadingCategories && (
                    <div className="owl-item loading-indicator" style={{ height: '100%' }}>
                      <Link to="#" style={{ minHeight: '40px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                        <div className="spinner-border text-primary" role="status" style={{ width: '1.5rem', height: '1.5rem' }}>
                          <span className="visually-hidden">Loading...</span>
                        </div>
                      </Link>
                      <h6 style={{ visibility: 'hidden' }}>
                        <Link to="#">Placeholder</Link>
                      </h6>
                      <span style={{ visibility: 'hidden' }}>0 Items</span>
                    </div>
                  )}

                  {/* Error indicator */}
                  {categoryError && (
                    <div className="owl-item error-indicator" style={{ height: '100%' }}>
                      <Link to="#" style={{ minHeight: '40px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                        <i className="ti ti-alert-circle text-danger" style={{ fontSize: '1.5rem' }}></i>
                      </Link>
                      <h6>
                        <Link to="#" className="text-danger">Error</Link>
                      </h6>
                      <span className="text-danger">{categoryError}</span>
                    </div>
                  )}

                  {/* Map through categories from API */}
                  {categories.map((category, index) => {
                    // Create a slug/id from the category name for the tab
                    const categoryId = category.id;

                    // Calculate number of products in this category using a flexible approach
                    const categoryProductCount = products.filter(product =>
                      product.productCategoryId === categoryId ||
                      product.productCategory?.id === categoryId ||
                      product.categoryId === categoryId ||
                      product.category?.id === categoryId
                    ).length;

                    return (
                      <div
                        key={category.id || index}
                        onClick={() => setActiveTab(categoryId)}
                        className={`owl-item ${activeTab === categoryId ? 'active' : ''}`}
                        id={categoryId}
                      >
                        <Link to="#" style={{ minHeight: '40px', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                          {category.image || category.img ? (
                            <ImageWithBasePath
                              src={category.image || category.img}
                              alt={category.name}
                              style={{ width: '40px', height: '40px' }}
                            />
                          ) : (
                            <div style={{ width: '40px', height: '40px', display: 'flex', alignItems: 'center', justifyContent: 'center', backgroundColor: '#f5f5f5', borderRadius: '50%' }}>
                              <i className="ti ti-category" style={{ fontSize: '20px', color: '#666' }}></i>
                            </div>
                          )}
                        </Link>
                        <h6>
                          <Link to="#">{category.name}</Link>
                        </h6>
                        <span>{categoryProductCount} Items</span>
                      </div>
                    );
                  })}
                </Slider>
                <div className="pos-products">
                  <div className="d-flex align-items-center justify-content-between">
                    <h4 className="mb-3">Products</h4>
                    <div className="input-icon-start pos-search position-relative mb-3">
                      <span className="input-icon-addon">
                        <i className="ti ti-search" />
                      </span>
                      <input
                        type="text"
                        className="form-control"
                        placeholder="Search Product"
                        value={searchQuery}
                        onChange={handleSearchChange}
                      />
                    </div>
                  </div>

                  {/* Loading indicator for products */}
                  {loadingProducts && (
                    <div className="text-center my-4">
                      <div className="spinner-border text-primary" role="status">
                        <span className="visually-hidden">Loading products...</span>
                      </div>
                      <p className="mt-2">Loading products...</p>
                    </div>
                  )}

                  {/* Error indicator for products */}
                  {productError && (
                    <div className="alert alert-danger" role="alert">
                      <i className="ti ti-alert-circle me-2"></i>
                      {productError}
                    </div>
                  )}
                  <div className="tabs_container">
                    <div className={`tab_content active`} data-tab="all">
                      {filteredProducts.length === 0 && !loadingProducts && !productError ? (
                        <div className="text-center my-4">
                          <i className="ti ti-box fs-3 text-muted"></i>
                          <p className="mt-2">No products found</p>
                        </div>
                      ) : (
                        <div className="row">
                          {filteredProducts.map((product, index) => {
                            let defaultUom = product.productUOM.find(uom => uom.isMainUom === true);
                            if (defaultUom == null && product.productUOM.length > 0) {
                              defaultUom = product.productUOM[0];
                            }

                            let price = defaultUom?.effectivedProductPrice?.price || 0;
                            price = price.toFixed(product.currency?.precision || 2);

                            // Check if product is in order list
                            const isInOrderList = orderItems.some(item => item.id === product.id);

                            return (
                              <div className="col-sm-6 col-md-6 col-lg-4 col-xl-3" key={product.id || index}>
                                <div
                                  className={`product-info card ${isInOrderList ? 'active' : ''}`}
                                  onClick={(e) => handleProductClick(product, e)}
                                  title={isInOrderList ? "Click to remove from order" : "Click to add to order"}
                                  tabIndex="0"
                                >
                                  <Link to="#" className="pro-img">
                                    <ImageWithBasePath
                                      src={product.image || product.img || "assets/img/products/pos-product-01.svg"}
                                      alt={product.name}
                                    />
                                    <span className={isInOrderList ? 'visible' : ''}>
                                      <i className="ti ti-circle-check-filled" />
                                    </span>
                                  </Link>
                                  <h6 className="cat-name">
                                    <Link to="#">{product.productCategory?.name || 'Uncategorized'}</Link>
                                  </h6>
                                  <h6 className="product-name">
                                    <Link to="#">{product.name}</Link>
                                  </h6>
                                  <div className="d-flex align-items-center justify-content-between price">
                                    <span>
                                      {defaultUom?.effectivedProductPrice?.fractionQty || 1} { defaultUom?.uomPrimary?.name || 'Pcs' }
                                    </span>
                                    <p>{formatCurrency(price)}</p>
                                  </div>
                                  {isInOrderList && (
                                    <div className="added-badge">
                                      <span className="badge bg-success">
                                        <i className="ti ti-check"></i>
                                        <i className="ti ti-x remove-icon" title="Click to remove"></i>
                                      </span>
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* /Products */}
            {/* Order Details */}
            <div className="col-md-12 col-lg-5 col-xl-4 ps-0 theiaStickySidebar d-lg-flex">
                <aside className="product-order-list bg-secondary-transparent flex-fill">
                    <div className="card">
                        <div className="card-body">
                            <div className="order-head d-flex align-items-center justify-content-between w-100">
                                <div>
                                    <h3>Order List</h3>
                                </div>
                                <div className="d-flex align-items-center gap-2">
                                    <span className="badge badge-dark fs-10 fw-medium badge-xs">
                                        #ORD123
                                    </span>
                                    <Link className="link-danger fs-16" to="#">
                                        <i className="ti ti-trash-x-filled" />
                                    </Link>
                                </div>
                            </div>
                            <div className="customer-info block-section">
                                <h5 className="mb-2">Customer Information</h5>
                                <div className="d-flex align-items-center gap-2">
                                    <div className="flex-grow-1">
                                        <Select
                                            options={customerOptions}
                                            classNamePrefix="react-select select"
                                            placeholder="Choose a Customer"
                                            value={customerOptions.find(option => option.customer?.id === selectedCustomer?.id) || null}
                                            onChange={handleCustomerChange}
                                            isSearchable={true}
                                        />
                                    </div>
                                    <Link
                                        to="#"
                                        className="btn btn-teal btn-icon fs-20"
                                        data-bs-toggle="modal"
                                        data-bs-target="#create"
                                    >
                                        <i className="ti ti-user-plus" />
                                    </Link>
                                    <Link
                                        to="#"
                                        className="btn btn-info btn-icon fs-20"
                                        data-bs-toggle="modal"
                                        data-bs-target="#barcode"
                                    >
                                        <i className="ti ti-scan" />
                                    </Link>
                                </div>
                                {selectedCustomer && selectedCustomer.id !== 'walk-in' && (
                                    <div className="customer-item border border-orange bg-orange-100 d-flex align-items-center justify-content-between flex-wrap gap-2 mt-3">
                                        <div>
                                            <h6 className="fs-16 fw-bold mb-1">{selectedCustomer.name}</h6>
                                            <div className="d-inline-flex align-items-center gap-2 customer-info-details">
                                                {selectedCustomer.phone && (
                                                    <p className="fs-13 d-inline-flex align-items-center gap-1">
                                                        <i className="ti ti-phone fs-12"></i>
                                                        <span className="text-muted">
                                                            {selectedCustomer.phone}
                                                        </span>
                                                    </p>
                                                )}
                                                {selectedCustomer.email && (
                                                    <p className="fs-13 d-inline-flex align-items-center gap-1">
                                                        <i className="ti ti-mail fs-12"></i>
                                                        <span className="text-muted">
                                                            {selectedCustomer.email}
                                                        </span>
                                                    </p>
                                                )}
                                            </div>
                                            {selectedCustomer.address && (
                                                <p className="fs-12 text-muted mb-0">
                                                    <i className="ti ti-map-pin fs-12 me-1"></i>
                                                    {selectedCustomer.address}
                                                </p>
                                            )}
                                        </div>
                                        <Link to="#" className="close-icon" onClick={() => {
                                            // Reset to walk-in customer
                                            const walkInCustomer = customerOptions.find(option => option.customer?.id === 'walk-in')?.customer;
                                            if (walkInCustomer) {
                                                setSelectedCustomer(walkInCustomer);
                                                setSelectedCustomerState(walkInCustomer);
                                            }
                                        }}>
                                            <i className="ti ti-x" />
                                        </Link>
                                    </div>
                                )}
                            </div>
                            <div className="product-added block-section">
                                <div className="head-text d-flex align-items-center justify-content-between mb-3">
                                    <div className="d-flex align-items-center">
                                        <h5 className="me-2">Order Details</h5>
                                        <div className="badge bg-light text-gray-9 fs-12 fw-semibold py-2 border rounded">
                                            Items : <span className="text-teal">{orderItems.length}</span>
                                        </div>
                                    </div>
                                    <div className="d-flex align-items-center gap-2">
                                        {selectedCustomer && selectedCustomer.id !== 'walk-in' && (
                                            <>
                                                <Link
                                                    to="#"
                                                    className="d-flex align-items-center fs-10 fw-medium text-primary"
                                                    onClick={handleViewCustomerOrdersClick}
                                                    title="View saved orders for this customer"
                                                >
                                                    <i className="ti ti-history me-1"></i>
                                                    Orders
                                                </Link>
                                                {orderItems.length > 0 && (
                                                    <Link
                                                        to="#"
                                                        className="d-flex align-items-center fs-10 fw-medium text-success"
                                                        onClick={handleSaveOrderClick}
                                                        title="Save current order for this customer"
                                                    >
                                                        <i className="ti ti-device-floppy me-1"></i>
                                                        Save
                                                    </Link>
                                                )}
                                            </>
                                        )}
                                        <Link
                                            to="#"
                                            className="d-flex align-items-center clear-icon fs-10 fw-medium"
                                            onClick={handleClearOrders}
                                        >
                                            Clear all
                                        </Link>
                                    </div>
                                </div>
                                <div className="product-wrap">
                                    {orderItems.length === 0 ? (
                                        <div className="empty-cart">
                                            <div className="fs-24 mb-1">
                                                <i className="ti ti-shopping-cart" />
                                            </div>
                                            <p className="fw-bold">No Products Selected</p>
                                        </div>
                                    ) : (
                                        <div className="product-list border-0 p-0">
                                            <div className="table-responsive">
                                                <table className="table table-borderless">
                                                    <thead>
                                                        <tr>
                                                            <th className="fw-bold bg-light">Item</th>
                                                            <th className="fw-bold bg-light">QTY</th>
                                                            <th className="fw-bold bg-light text-center">Tax</th>
                                                            <th className="fw-bold bg-light text-end">Cost</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        {orderItems.map((item, index) => (
                                                            <tr key={item.id || index}>
                                                                <td>
                                                                    <div className="d-flex align-items-center">
                                                                        <Link
                                                                            className="delete-icon"
                                                                            to="#"
                                                                            onClick={() => handleRemoveProduct(item.id)}
                                                                        >
                                                                            <i className="ti ti-trash-x-filled" />
                                                                        </Link>
                                                                        <h6 className="fs-13 fw-normal">
                                                                            <Link
                                                                                to="#"
                                                                                className="link-default"
                                                                            >
                                                                                {item.name}
                                                                            </Link>
                                                                        </h6>
                                                                    </div>
                                                                </td>
                                                                <td>
                                                                    <div className="qty-item m-0">
                                                                        <CartCounter
                                                                            defaultValue={item.quantity}
                                                                            onChange={(value) => handleQuantityChange(item.id, value)}
                                                                        />
                                                                    </div>
                                                                </td>
                                                                <td className="fs-12 text-center">
                                                                    {paymentSummary.taxRate > 0 ? (
                                                                        <span className="badge bg-primary-light text-primary">
                                                                            {paymentSummary.taxRate}%
                                                                        </span>
                                                                    ) : (
                                                                        <span className="text-muted fs-11">
                                                                            No Tax
                                                                        </span>
                                                                    )}
                                                                </td>
                                                                <td className="fs-13 fw-semibold text-gray-9 text-end">
                                                                    {formatCurrency(item.price * item.quantity)}
                                                                </td>
                                                            </tr>
                                                        ))}
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    )}
                                </div>
                                {discountSettings.customPercentage > 0 && (
                                  <div className="discount-item d-flex align-items-center justify-content-between bg-purple-transparent mt-3 flex-wrap gap-2">
                                      <div className="d-flex align-items-center">
                                          <span className="bg-purple discount-icon br-5 flex-shrink-0 me-2">
                                              <img src="assets/img/icons/discount-icon.svg" alt="img" />
                                          </span>
                                          <div>
                                              <h6 className="fs-14 fw-bold text-purple mb-1">
                                                  Custom Discount {discountSettings.customPercentage}%
                                              </h6>
                                              <p className="mb-0">
                                                  Applied to all items in order
                                              </p>
                                          </div>
                                      </div>
                                      <Link
                                        to="#"
                                        className="close-icon"
                                        onClick={() => {
                                          const newSettings = { ...discountSettings, customPercentage: 0 };
                                          setDiscountSettings(newSettings);
                                          saveDiscountSettings(newSettings);

                                          // Recalculate order total with independent tax and service charge calculations
                                          recalculateOrderTotal();
                                        }}
                                      >
                                          <i className="ti ti-trash" />
                                      </Link>
                                  </div>
                                )}
                            </div>
                            <div className="order-total bg-total bg-white p-0">
                                <h5 className="mb-3">Payment Summary</h5>
                                <table className="table table-responsive table-borderless">
                                    <tbody>
                                        {shouldShowTax() && (
                                        <tr>
                                            <td>
                                                Tax
                                                {paymentSummary.taxRate > 0 && (
                                                  <span className="ms-2 badge bg-info">
                                                    {paymentSummary.taxRate}%
                                                  </span>
                                                )}
                                                <Link
                                                    to="#"
                                                    className="ms-3 link-default"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#order-tax-view"
                                                    title="View tax information"
                                                >
                                                    <i className="ti ti-eye" />
                                                </Link>
                                            </td>
                                            <td className="text-gray-9 text-end">{formatCurrency(getCurrentTaxAmount())}</td>
                                        </tr>
                                        )}
                                        <tr>
                                            <td>
                                                Voucher
                                                <Link
                                                    to="#"
                                                    className="ms-3 link-default"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#coupon-code"
                                                >
                                                    <i className="ti ti-edit" />
                                                </Link>
                                            </td>
                                            <td className="text-gray-9 text-end">{formatCurrency(orderTotal.voucher)}</td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <span className="text-danger">Discount</span>
                                                {discountSettings.customPercentage > 0 && (
                                                  <span className="ms-2 badge bg-info">
                                                    {discountSettings.customPercentage}%
                                                  </span>
                                                )}
                                                <Link
                                                    to="#"
                                                    className="ms-3 link-default"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#discount"
                                                >
                                                    <i className="ti ti-edit" />
                                                </Link>
                                            </td>
                                            <td className="text-danger text-end">{formatCurrency(orderTotal.discount)}</td>
                                        </tr>
                                        {shouldShowServiceCharges() && (
                                        <tr>
                                            <td>
                                                Service Charges
                                                {getServiceChargePercentage() > 0 && (
                                                  <span className="ms-2 badge bg-info">
                                                    {getServiceChargePercentage()}%
                                                  </span>
                                                )}
                                            </td>
                                            <td className="text-gray-9 text-end">{formatCurrency(orderTotal.serviceCharges)}</td>
                                        </tr>
                                        )}
                                        <tr>
                                            <td>
                                                <div className="form-check form-switch">
                                                    <input
                                                        className="form-check-input"
                                                        type="checkbox"
                                                        role="switch"
                                                        id="round"
                                                        checked={paymentSummary.roundOff}
                                                        onChange={(e) => {
                                                          const newPaymentSummary = { ...paymentSummary, roundOff: e.target.checked };
                                                          setPaymentSummary(newPaymentSummary);
                                                          savePaymentSummary(newPaymentSummary);

                                                          // Recalculate order total with independent tax and service charge calculations
                                                          recalculateOrderTotal();
                                                        }}
                                                    />
                                                    <label className="form-check-label" htmlFor="round">
                                                        Rounding Adjustment
                                                    </label>
                                                </div>
                                            </td>
                                            <td className="text-gray-9 text-end">{formatCurrency(orderTotal.roundOffAmount)}</td>
                                        </tr>
                                        <tr>
                                            <td>Sub Total</td>
                                            <td className="text-gray-9 text-end">{formatCurrency(orderTotal.subtotal)}</td>
                                        </tr>
                                        <tr>
                                            <td className="fw-bold border-top border-dashed">
                                                Total Payable
                                            </td>
                                            <td className="text-gray-9 fw-bold text-end border-top border-dashed">
                                                {formatCurrency(orderTotal.total)}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    {/* Payment Listing Section */}
                    {currentOrderPayments.length > 0 && (
                      <div className="card payment-listing">
                        <div className="card-body">
                          <div className="d-flex align-items-center justify-content-between mb-3">
                            <h5 className="mb-0">Payment History</h5>
                            <span className="badge bg-info">
                              {currentOrderPayments.length} Payment{currentOrderPayments.length !== 1 ? 's' : ''}
                            </span>
                          </div>
                          <div className="payment-list">
                            {currentOrderPayments.map((payment, index) => (
                              <div key={payment.id} className="payment-item-row d-flex align-items-center justify-content-between p-2 mb-2 bg-light rounded">
                                <div className="payment-info">
                                  <div className="d-flex align-items-center gap-2">
                                    <span className="badge bg-success fs-10">#{index + 1}</span>
                                    <span className="fw-medium text-capitalize">{payment.paymentMethod}</span>
                                    {payment.paymentType && payment.paymentType !== payment.paymentMethod && (
                                      <span className="text-muted">({payment.paymentType})</span>
                                    )}
                                  </div>
                                  <small className="text-muted">
                                    {new Date(payment.timestamp).toLocaleTimeString()}
                                  </small>
                                </div>
                                <div className="payment-amount text-end">
                                  <div className="fw-bold text-success">
                                    {formatPaymentCurrency(payment.amount)}
                                  </div>
                                  {payment.change > 0 && (
                                    <small className="text-muted">
                                      Change: {formatPaymentCurrency(payment.change)}
                                    </small>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>

                          {/* Payment Balance Summary */}
                          <div className="payment-balance-summary mt-3 p-3 bg-primary-transparent rounded">
                            <div className="row g-2">
                              <div className="col-6">
                                <div className="text-center">
                                  <small className="text-muted d-block">Total Paid</small>
                                  <span className="fw-bold text-success">
                                    {formatPaymentCurrency(paymentBalance.totalPaid)}
                                  </span>
                                </div>
                              </div>
                              <div className="col-6">
                                <div className="text-center">
                                  <small className="text-muted d-block">
                                    {paymentBalance.remaining > 0 ? 'Remaining' : paymentBalance.remaining < 0 ? 'Overpaid' : 'Fully Paid'}
                                  </small>
                                  <span className={`fw-bold ${paymentBalance.remaining > 0 ? 'text-warning' : paymentBalance.remaining < 0 ? 'text-info' : 'text-success'}`}>
                                    {formatPaymentCurrency(Math.abs(paymentBalance.remaining))}
                                  </span>
                                </div>
                              </div>
                            </div>
                            {paymentBalance.isFullyPaid && (
                              <div className="text-center mt-2">
                                <span className="badge bg-success">
                                  <i className="ti ti-check me-1"></i>
                                  Order Fully Paid
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    )}

                    <div className="card payment-method">
                        <div className="card-body">
                            <div className="d-flex align-items-center justify-content-between mb-3">
                                <h5 className="mb-0">Select Payment</h5>
                                {isLoadingPaymentMethods && (
                                    <div className="spinner-border spinner-border-sm text-primary" role="status">
                                        <span className="visually-hidden">Loading...</span>
                                    </div>
                                )}
                            </div>

                            {isLoadingPaymentMethods ? (
                                <div className="text-center py-4">
                                    <div className="spinner-border text-primary" role="status">
                                        <span className="visually-hidden">Loading payment methods...</span>
                                    </div>
                                    <p className="text-muted mt-2">Loading payment methods from API...</p>
                                </div>
                            ) : paymentMethods.length === 0 ? (
                                <div className="text-center py-4">
                                    <i className="ti ti-credit-card-off fs-1 text-muted mb-2"></i>
                                    <p className="text-muted">No payment methods available</p>
                                </div>
                            ) : (
                                <div className="row align-items-center methods g-2">
                                    {paymentMethods.map((method) => (
                                        <div key={method.id} className="col-sm-6 col-md-4 d-flex">
                                            <Link
                                                to="#"
                                                className={`payment-item d-flex align-items-center justify-content-center p-2 flex-fill ${!method.isActive ? 'disabled opacity-50' : ''}`}
                                                data-bs-toggle={method.isActive ? "modal" : ""}
                                                data-bs-target={method.isActive ? getPaymentModalTarget(method) : ""}
                                                title={method.description || method.label}
                                                style={{
                                                    pointerEvents: method.isActive ? 'auto' : 'none',
                                                    cursor: method.isActive ? 'pointer' : 'not-allowed'
                                                }}
                                            >
                                                <img
                                                    src={method.icon}
                                                    className="me-2"
                                                    alt={method.label}
                                                    onError={(e) => {
                                                        // Fallback to default icon if image fails to load
                                                        e.target.src = "assets/img/icons/cash-icon.svg";
                                                    }}
                                                />
                                                <p className="fs-14 fw-medium mb-0">{method.label}</p>
                                                {!method.isActive && (
                                                    <span className="badge bg-secondary ms-2 fs-10">Disabled</span>
                                                )}
                                            </Link>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>
                    <div className="btn-row d-flex align-items-center justify-content-between gap-3">
                        <Link
                            to="#"
                            className="btn btn-white d-flex align-items-center justify-content-center flex-fill m-0"
                            data-bs-toggle="modal"
                            data-bs-target="#hold-order"
                        >
                            <i className="ti ti-printer me-2" />
                            Print Order
                        </Link>
                        <Link
                            to="#"
                            className="btn btn-secondary d-flex align-items-center justify-content-center flex-fill m-0"
                            data-bs-toggle="modal"
                            data-bs-target="#payment-completed"
                        >
                            <i className="ti ti-shopping-cart me-2" />
                            Place Order
                        </Link>
                    </div>
                </aside>
            </div>
            {/* /Order Details */}
          </div>
        </div>
      </div>
      <PosModals/>
    </div>
  );
};

export default Pos;
